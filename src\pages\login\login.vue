<route lang="json">
{
  "type": "page",
  "layout": "default",
  "style": {
    "navigationBarTitleText": "登录",
    "navigationStyle": "custom"
  }
}
</route>

<script setup lang="ts">
import type { ICaptcha, ILoginAttempts } from '@/api/types/login'
import { onMounted, reactive, ref } from 'vue'
import { getCaptcha, getLoginAttempts } from '@/api/login'
import { useUserStore } from '@/store/user'

// 使用用户store
const userStore = useUserStore()

// 获取页面参数
const pages = getCurrentPages()
const currentPage = pages[pages.length - 1] as any
const redirectUrl = currentPage.options?.redirect || '/pages/statistics/statistics'

console.log('登录页面 - 重定向URL:', redirectUrl)

// 登录表单数据
const loginForm = reactive({
  phone: '',
  password: '',
  captcha_code: '',
})

// 验证码相关
const captcha = ref<ICaptcha>({
  captcha_id: '',
  captcha_image: '',
  expires_in: 0,
})

// 登录尝试状态
const loginAttempts = ref<ILoginAttempts>({
  remaining_attempts: 5,
  is_locked: false,
  locked_until: null,
  requires_captcha: false,
})

// 控制状态
const loading = ref(false)
const showPassword = ref(false)
const rememberPassword = ref(false)
const checkboxGroup = ref([])

// 样式配置
const inputStyle = {
  backgroundColor: '#f8f9fa',
  borderRadius: '16px',
  padding: '20px',
  fontSize: '16px',
  border: 'none',
}

const loginBtnStyle = {
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  borderRadius: '16px',
  height: '56px',
  fontSize: '18px',
  fontWeight: 'bold',
}

// 验证手机号格式
function validatePhone(phone: string) {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

// 获取验证码
async function fetchCaptcha() {
  try {
    const res = await getCaptcha()
    captcha.value = res.data
    console.log('获取验证码成功:', res.data)
  }
  catch (error) {
    console.error('获取验证码失败:', error)
    uni.showToast({
      title: '获取验证码失败',
      icon: 'none',
    })
  }
}

// 刷新验证码
function refreshCaptcha() {
  fetchCaptcha()
}

// 检查登录尝试状态
async function checkLoginAttempts(phone: string) {
  if (!phone || !validatePhone(phone)) {
    return
  }

  try {
    const res = await getLoginAttempts(phone)
    const attemptData = res.data || res
    loginAttempts.value = attemptData
    console.log('登录尝试状态:', attemptData)

    // 如果需要验证码，获取验证码
    if (attemptData?.requires_captcha) {
      await fetchCaptcha()
    }
  }
  catch (error) {
    console.error('检查登录尝试状态失败:', error)
    // 静默处理错误，设置默认值
    loginAttempts.value = {
      remaining_attempts: 5,
      is_locked: false,
      locked_until: null,
      requires_captcha: false,
    }
  }
}

// 登录处理
async function handleLogin() {
  // 表单验证
  if (!loginForm.phone.trim()) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none',
    })
    return
  }

  if (!validatePhone(loginForm.phone)) {
    uni.showToast({
      title: '请输入正确的手机号格式',
      icon: 'none',
    })
    return
  }

  if (!loginForm.password.trim()) {
    uni.showToast({
      title: '请输入密码',
      icon: 'none',
    })
    return
  }

  // 如果需要验证码，验证验证码
  if (loginAttempts.value?.requires_captcha) {
    if (!loginForm.captcha_code.trim()) {
      uni.showToast({
        title: '请输入验证码',
        icon: 'none',
      })
      return
    }
  }

  loading.value = true

  try {
    // 构建登录参数
    const loginParams: any = {
      phone: loginForm.phone,
      password: loginForm.password,
    }

    // 如果需要验证码，添加验证码参数
    if (loginAttempts.value?.requires_captcha) {
      loginParams.captcha_id = captcha.value.captcha_id
      loginParams.captcha_code = loginForm.captcha_code
    }

    await userStore.login(loginParams)

    // 保存记住的密码
    saveCredentials()

    // 登录成功，跳转到目标页面或首页
    const targetUrl = decodeURIComponent(redirectUrl)
    console.log('登录成功，跳转到:', targetUrl)

    // 如果是首页或统计页面，使用reLaunch
    if (targetUrl === '/pages/statistics/statistics' || targetUrl.includes('/pages/statistics/statistics')) {
      uni.reLaunch({
        url: targetUrl,
      })
    }
    else {
      // 其他页面使用redirectTo
      uni.redirectTo({
        url: targetUrl,
      })
    }
  }
  catch (error) {
    console.error('登录失败:', error)

    // 登录失败后重新检查登录尝试状态
    await checkLoginAttempts(loginForm.phone)

    // 如果需要验证码，清空验证码输入
    if (loginAttempts.value?.requires_captcha) {
      loginForm.captcha_code = ''
      await fetchCaptcha() // 刷新验证码
    }
  }
  finally {
    loading.value = false
  }
}

// 保存/加载记住的密码
function saveCredentials() {
  if (rememberPassword.value) {
    uni.setStorageSync('rememberedPhone', loginForm.phone)
    uni.setStorageSync('rememberedPassword', loginForm.password)
    uni.setStorageSync('rememberPassword', true)
  }
  else {
    uni.removeStorageSync('rememberedPhone')
    uni.removeStorageSync('rememberedPassword')
    uni.removeStorageSync('rememberPassword')
  }
}

function loadSavedCredentials() {
  const savedRemember = uni.getStorageSync('rememberPassword')
  if (savedRemember) {
    rememberPassword.value = true
    checkboxGroup.value = ['remember']
    loginForm.phone = uni.getStorageSync('rememberedPhone') || ''
    loginForm.password = uni.getStorageSync('rememberedPassword') || ''
  }
}

// 处理记住密码复选框变化
function handleRememberChange(value: string[]) {
  rememberPassword.value = value.includes('remember')
}

// 忘记密码
function forgotPassword() {
  uni.showToast({
    title: '功能开发中',
    icon: 'none',
  })
}

// 注册账号
function register() {
  uni.showToast({
    title: '功能开发中',
    icon: 'none',
  })
}

// 监听手机号输入，自动检查登录尝试状态
function onPhoneInput() {
  if (validatePhone(loginForm.phone)) {
    checkLoginAttempts(loginForm.phone)
  }
}

// 页面加载时加载保存的凭据
onMounted(() => {
  loadSavedCredentials()
})
</script>

<template>
  <view class="login-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="navbar-title">
          <text class="title-main">
            兔场管理系统
          </text>
          <text class="title-sub">
            专业的养殖场管理平台
          </text>
        </view>
      </view>
    </view>

    <!-- 背景装饰 -->
    <view class="background-decoration">
      <view class="bg-gradient" />
      <view class="floating-elements">
        <view class="floating-circle circle-1" />
        <view class="floating-circle circle-2" />
        <view class="floating-circle circle-3" />
        <view class="floating-circle circle-4" />
      </view>
      <view class="bg-pattern" />
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- Logo区域 -->
      <view class="logo-section">
        <view class="logo-container">
          <view class="logo-icon">
            <text class="logo-emoji">
              🐰
            </text>
          </view>
          <view class="logo-text">
            <text class="app-name">
              兔场管理
            </text>
            <text class="app-slogan">
              智能化养殖，科学化管理
            </text>
          </view>
        </view>
      </view>

      <!-- 登录表单 -->
      <view class="login-form-container">
        <view class="form-card">
          <view class="form-header">
            <text class="form-title">
              欢迎登录
            </text>
            <text class="form-subtitle">
              请输入您的账号信息
            </text>
          </view>

          <view class="form-content">
            <!-- 手机号输入 -->
            <view class="input-group">
              <view class="input-label">
                <text class="label-icon">
                  📱
                </text>
                <text class="label-text">
                  手机号
                </text>
              </view>
              <u-input
                v-model="loginForm.phone"
                placeholder="请输入手机号"
                type="number"
                class="modern-input"
                border="none"
                :custom-style="inputStyle"
                clearable
                @input="onPhoneInput"
              />
            </view>

            <!-- 密码输入 -->
            <view class="input-group">
              <view class="input-label">
                <text class="label-icon">
                  🔒
                </text>
                <text class="label-text">
                  密码
                </text>
              </view>
              <u-input
                v-model="loginForm.password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请输入密码"
                class="modern-input"
                border="none"
                :custom-style="inputStyle"
                clearable
              >
                <template #suffix>
                  <view class="password-toggle" @click="showPassword = !showPassword">
                    <text class="toggle-icon">
                      {{ showPassword ? '👁️' : '🙈' }}
                    </text>
                  </view>
                </template>
              </u-input>
            </view>

            <!-- 验证码输入 -->
            <view v-if="loginAttempts?.requires_captcha" class="input-group">
              <view class="input-label">
                <text class="label-icon">
                  🔢
                </text>
                <text class="label-text">
                  验证码
                </text>
              </view>
              <view class="captcha-container">
                <u-input
                  v-model="loginForm.captcha_code"
                  placeholder="请输入验证码"
                  class="modern-input captcha-input"
                  border="none"
                  :custom-style="inputStyle"
                  clearable
                />
                <view class="captcha-image" @click="refreshCaptcha">
                  <image
                    v-if="captcha.captcha_image"
                    :src="captcha.captcha_image"
                    class="captcha-img"
                    mode="aspectFit"
                  />
                  <view v-else class="captcha-placeholder">
                    <text class="placeholder-text">
                      点击获取
                    </text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 记住密码 -->
            <view class="form-options">
              <view class="remember-section">
                <u-checkbox-group v-model="checkboxGroup">
                  <u-checkbox
                    name="remember"
                    shape="circle"
                    size="18"
                    active-color="#667eea"
                    @change="handleRememberChange"
                  >
                    <text class="remember-text">
                      记住密码
                    </text>
                  </u-checkbox>
                </u-checkbox-group>
              </view>
              <view class="forgot-section">
                <text class="forgot-link" @click="forgotPassword">
                  忘记密码？
                </text>
              </view>
            </view>

            <!-- 登录按钮 -->
            <view class="button-section">
              <u-button
                type="primary"
                class="login-btn"
                :custom-style="loginBtnStyle"
                :loading="loading"
                @click="handleLogin"
              >
                <text class="btn-icon">
                  🚀
                </text>
                <text class="btn-text">
                  立即登录
                </text>
              </u-button>
            </view>

            <!-- 注册链接 -->
            <view class="register-section">
              <text class="register-text">
                还没有账号？
              </text>
              <text class="register-link" @click="register">
                立即注册
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="footer-section">
      <text class="footer-text">
        © 2024 兔场管理系统 版权所有
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  background: #f8fafc;
  position: relative;
  overflow: hidden;
}

// 自定义导航栏
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: var(--status-bar-height, 44rpx) 0 0;
}

.navbar-content {
  padding: 20rpx 40rpx;
  text-align: center;

  .navbar-title {
    .title-main {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #1a202c;
      margin-bottom: 4rpx;
    }

    .title-sub {
      font-size: 22rpx;
      color: #64748b;
    }
  }
}

// 背景装饰
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;

  .bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0.1;
  }

  .floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    .floating-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(102, 126, 234, 0.1);
      animation: float 8s ease-in-out infinite;

      &.circle-1 {
        width: 200rpx;
        height: 200rpx;
        top: 10%;
        left: -50rpx;
        animation-delay: 0s;
      }

      &.circle-2 {
        width: 150rpx;
        height: 150rpx;
        top: 30%;
        right: -30rpx;
        animation-delay: 2s;
      }

      &.circle-3 {
        width: 120rpx;
        height: 120rpx;
        bottom: 40%;
        left: 20%;
        animation-delay: 4s;
      }

      &.circle-4 {
        width: 180rpx;
        height: 180rpx;
        bottom: 10%;
        right: 10%;
        animation-delay: 6s;
      }
    }
  }

  .bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(102,126,234,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(102,126,234,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(102,126,234,0.1)"/></svg>');
    opacity: 0.3;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-30rpx) rotate(180deg);
  }
}

// 主要内容
.main-content {
  padding: calc(152rpx + var(--status-bar-height, 44rpx)) 40rpx 40rpx;
  position: relative;
  z-index: 1;
}

// Logo区域
.logo-section {
  text-align: center;
  margin-bottom: 60rpx;

  .logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    .logo-icon {
      width: 120rpx;
      height: 120rpx;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 30rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 24rpx;
      box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.3);

      .logo-emoji {
        font-size: 60rpx;
      }
    }

    .logo-text {
      text-align: center;

      .app-name {
        display: block;
        font-size: 48rpx;
        font-weight: bold;
        color: #1a202c;
        margin-bottom: 8rpx;
      }

      .app-slogan {
        font-size: 26rpx;
        color: #64748b;
      }
    }
  }
}

// 登录表单容器
.login-form-container {
  .form-card {
    background: white;
    border-radius: 24rpx;
    padding: 40rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid rgba(0, 0, 0, 0.04);

    .form-header {
      text-align: center;
      margin-bottom: 40rpx;

      .form-title {
        display: block;
        font-size: 40rpx;
        font-weight: bold;
        color: #1a202c;
        margin-bottom: 8rpx;
      }

      .form-subtitle {
        font-size: 26rpx;
        color: #64748b;
      }
    }

    .form-content {
      .input-group {
        margin-bottom: 32rpx;

        .input-label {
          display: flex;
          align-items: center;
          margin-bottom: 12rpx;

          .label-icon {
            font-size: 24rpx;
            margin-right: 8rpx;
          }

          .label-text {
            font-size: 28rpx;
            font-weight: 500;
            color: #374151;
          }
        }

        .captcha-container {
          display: flex;
          gap: 16rpx;
          align-items: center;

          .captcha-input {
            flex: 1;
          }

          .captcha-image {
            width: 160rpx;
            height: 80rpx;
            background: #f1f5f9;
            border-radius: 12rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;

            &:active {
              background: #e2e8f0;
              transform: scale(0.98);
            }

            .captcha-img {
              width: 100%;
              height: 100%;
              border-radius: 12rpx;
            }

            .captcha-placeholder {
              .placeholder-text {
                font-size: 22rpx;
                color: #64748b;
              }
            }
          }
        }

        .password-toggle {
          padding: 8rpx;
          cursor: pointer;

          .toggle-icon {
            font-size: 24rpx;
          }
        }
      }

      .form-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 40rpx;

        .remember-section {
          .remember-text {
            font-size: 24rpx;
            color: #64748b;
            margin-left: 8rpx;
          }
        }

        .forgot-section {
          .forgot-link {
            font-size: 24rpx;
            color: #667eea;
            cursor: pointer;

            &:active {
              color: #5a67d8;
            }
          }
        }
      }

      .button-section {
        margin-bottom: 32rpx;

        .login-btn {
          width: 100%;
          height: 88rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12rpx;
          border-radius: 16rpx;
          font-weight: bold;

          .btn-icon {
            font-size: 24rpx;
          }

          .btn-text {
            font-size: 28rpx;
          }
        }
      }

      .register-section {
        text-align: center;

        .register-text {
          font-size: 24rpx;
          color: #64748b;
          margin-right: 8rpx;
        }

        .register-link {
          font-size: 24rpx;
          color: #667eea;
          cursor: pointer;

          &:active {
            color: #5a67d8;
          }
        }
      }
    }
  }
}

// 底部信息
.footer-section {
  text-align: center;
  padding: 40rpx;
  margin-top: 40rpx;

  .footer-text {
    font-size: 22rpx;
    color: #94a3b8;
  }
}
</style>
