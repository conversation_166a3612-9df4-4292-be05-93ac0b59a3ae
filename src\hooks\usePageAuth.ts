import { onLoad } from '@dcloudio/uni-app'
import { useUserStore } from '@/store'

const loginRoute = import.meta.env.VITE_LOGIN_URL || '/pages/login/login'

// 白名单页面 - 不需要登录就可以访问的页面
const whiteListPages = [
  '/pages/login/login',
  // 可以在这里添加其他不需要登录的页面
]

function isLogined() {
  const userStore = useUserStore()
  return userStore.isLoggedIn()
}

// 检查当前页面是否需要登录
export function usePageAuth() {
  onLoad((options) => {
    // 获取当前页面路径
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentPath = `/${currentPage.route}`

    console.log('页面认证检查 - 当前页面:', currentPath)

    // 检查是否在白名单中
    const isInWhiteList = whiteListPages.includes(currentPath)
    if (isInWhiteList) {
      console.log('页面认证检查 - 白名单页面，允许访问')
      return
    }

    // 检查是否已登录
    const hasLogin = isLogined()
    if (hasLogin) {
      console.log('页面认证检查 - 已登录，允许访问')
      return
    }

    // 未登录且不在白名单中，跳转到登录页
    console.log('页面认证检查 - 未登录，跳转到登录页')

    // 构建重定向URL
    const queryString = Object.entries(options || {})
      .map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`)
      .join('&')

    const currentFullPath = queryString ? `${currentPath}?${queryString}` : currentPath
    const redirectRoute = `${loginRoute}?redirect=${encodeURIComponent(currentFullPath)}`

    // 重定向到登录页
    uni.redirectTo({ url: redirectRoute })
  })
}
