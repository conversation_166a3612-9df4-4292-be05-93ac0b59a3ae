/**
 * 认证系统测试工具
 * 用于验证登录拦截功能是否正常工作
 */

import { useUserStore } from '@/store'

export class AuthTester {
  private userStore = useUserStore()

  /**
   * 测试登录状态检查
   */
  testLoginStatus() {
    console.log('=== 登录状态测试 ===')
    console.log('Token:', this.userStore.getToken())
    console.log('用户信息:', this.userStore.userInfo)
    console.log('是否已登录:', this.userStore.isLoggedIn())
    console.log('==================')
  }

  /**
   * 测试清除认证状态
   */
  testClearAuth() {
    console.log('=== 清除认证状态测试 ===')
    console.log('清除前 - 是否已登录:', this.userStore.isLoggedIn())
    this.userStore.clearAuth()
    console.log('清除后 - 是否已登录:', this.userStore.isLoggedIn())
    console.log('清除后 - Token:', this.userStore.getToken())
    console.log('========================')
  }

  /**
   * 测试页面跳转拦截
   */
  testPageNavigation() {
    console.log('=== 页面跳转拦截测试 ===')
    
    // 测试跳转到需要登录的页面
    const testPages = [
      '/pages/statistics/statistics',
      '/pages/profile/profile',
    ]

    testPages.forEach(page => {
      console.log(`测试跳转到: ${page}`)
      try {
        uni.navigateTo({
          url: page,
          success: () => {
            console.log(`✅ 成功跳转到: ${page}`)
          },
          fail: (err) => {
            console.log(`❌ 跳转失败: ${page}`, err)
          }
        })
      } catch (error) {
        console.log(`❌ 跳转异常: ${page}`, error)
      }
    })
    console.log('========================')
  }

  /**
   * 模拟登录
   */
  mockLogin() {
    console.log('=== 模拟登录测试 ===')
    
    // 模拟设置token和用户信息
    this.userStore.setToken('mock-token-12345')
    this.userStore.setUserInfo({
      id: 1,
      phone: '13800138000',
      username: '测试用户',
      avatar: '/static/images/default-avatar.png',
      is_active: true,
      is_superuser: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })

    console.log('模拟登录完成')
    console.log('登录状态:', this.userStore.isLoggedIn())
    console.log('==================')
  }

  /**
   * 测试退出登录API
   */
  async testLogoutAPI() {
    console.log('=== 退出登录API测试 ===')

    try {
      // 确保有token
      if (!this.userStore.getToken()) {
        console.log('⚠️ 没有token，先模拟登录')
        this.mockLogin()
      }

      console.log('调用退出登录API...')
      await this.userStore.logout()
      console.log('✅ 退出登录API调用成功')

      // 检查状态
      console.log('退出后登录状态:', this.userStore.isLoggedIn())
      console.log('退出后Token:', this.userStore.getToken())

    } catch (error) {
      console.log('❌ 退出登录API调用失败:', error)
    }

    console.log('========================')
  }

  /**
   * 运行所有测试
   */
  runAllTests() {
    console.log('🚀 开始运行认证系统测试...')

    // 1. 测试初始状态
    this.testLoginStatus()

    // 2. 测试页面跳转拦截（未登录状态）
    this.testPageNavigation()

    // 3. 模拟登录
    this.mockLogin()

    // 4. 测试登录后状态
    this.testLoginStatus()

    // 5. 测试页面跳转（已登录状态）
    this.testPageNavigation()

    // 6. 测试退出登录API
    this.testLogoutAPI()

    // 7. 测试清除后状态
    this.testLoginStatus()

    console.log('✅ 认证系统测试完成！')
  }
}

// 导出测试实例
export const authTester = new AuthTester()

// 在开发环境下，将测试工具挂载到全局对象上
if (import.meta.env.DEV) {
  // @ts-ignore
  globalThis.authTester = authTester
  console.log('🔧 开发模式：认证测试工具已挂载到 globalThis.authTester')
  console.log('使用方法：')
  console.log('- authTester.runAllTests() // 运行所有测试')
  console.log('- authTester.testLoginStatus() // 测试登录状态')
  console.log('- authTester.mockLogin() // 模拟登录')
  console.log('- authTester.testLogoutAPI() // 测试退出登录API')
  console.log('- authTester.testClearAuth() // 测试清除认证')
}
