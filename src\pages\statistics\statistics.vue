<route lang="json">
{
  "type": "home",
  "layout": "default",
  "style": {
    "navigationBarTitleText": "销售统计"
  }
}
</route>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'

import { useSalesStore } from '@/store/sales'
import { usePageAuth } from '@/hooks/usePageAuth'

// 页面认证检查
usePageAuth()

// 使用销售数据store
const salesStore = useSalesStore()

// 响应式数据
const showDatePicker = ref(false)
const showQuickEntry = ref(false)
const entryForm = ref({
  breed: '',
  quantity: '',
  price: '',
  date: new Date().toISOString().split('T')[0],
})

// 日历组件相关数据
const now = new Date()
const todayString = now.toISOString().split('T')[0]
// 设置最小日期为 5 个月前的今天
const minDate = ref(new Date(now.getFullYear(), now.getMonth() - 5, now.getDate()).getTime())
// 设置最大日期为今天
const maxDate = ref(now.getTime())
// 默认选中的日期（今天）
const defaultDate = ref([todayString])

// 计算属性
const totalSales = computed(() => salesStore.totalSales)
const totalQuantity = computed(() => salesStore.totalQuantity)
const todaySales = computed(() => salesStore.todaySales)
const recentSales = computed(() => salesStore.recentSales)

// 格式化日期显示
const formattedDate = computed(() => {
  if (!entryForm.value.date)
    return '选择日期'
  const date = new Date(entryForm.value.date)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long',
  })
})

// 样式配置
const inputStyle = {
  backgroundColor: '#f8f9fa',
  borderRadius: '12px',
  padding: '16px',
  fontSize: '16px',
}

const submitBtnStyle = {
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  borderRadius: '16px',
  height: '56px',
  fontSize: '16px',
  fontWeight: 'bold',
}

// 方法
function submitSale() {
  if (!entryForm.value.breed || !entryForm.value.quantity || !entryForm.value.price) {
    uni.showToast({
      title: '请填写完整信息',
      icon: 'none',
    })
    return
  }

  const saleData = {
    breed: entryForm.value.breed,
    quantity: Number(entryForm.value.quantity),
    price: Number(entryForm.value.price),
    total: Number(entryForm.value.quantity) * Number(entryForm.value.price),
    date: entryForm.value.date,
  }

  salesStore.addSale(saleData)

  // 重置表单
  entryForm.value = {
    breed: '',
    quantity: '',
    price: '',
    date: new Date().toISOString().split('T')[0],
  }

  uni.showToast({
    title: '录入成功',
    icon: 'success',
  })

  // 关闭弹窗
  showQuickEntry.value = false
}

function openDatePicker() {
  console.log('Opening date picker...', showDatePicker.value)
  showDatePicker.value = true
  console.log('After setting:', showDatePicker.value)
}

function onDateConfirm(e: any) {
  console.log('Calendar confirmed:', e)
  // u-calendar返回的数据格式可能是数组或对象
  if (e) {
    if (Array.isArray(e) && e.length > 0) {
      // 数组格式：["2024-07-26"]
      entryForm.value.date = e[0]
    }
    else if (e.fulldate) {
      // 对象格式：{ fulldate: "2024-07-26" }
      entryForm.value.date = e.fulldate
    }
    else if (typeof e === 'string') {
      // 字符串格式："2024-07-26"
      entryForm.value.date = e
    }
    console.log('Selected date:', entryForm.value.date)
  }
  showDatePicker.value = false
}

function onDateClose() {
  console.log('Calendar closed')
  showDatePicker.value = false
}

// 格式化当前日期
function formatCurrentDate() {
  const now = new Date()
  const month = now.getMonth() + 1
  const day = now.getDate()
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const weekday = weekdays[now.getDay()]
  return `${month}月${day}日 ${weekday}`
}

// 格式化日期显示
function formatDate(dateStr: string) {
  const date = new Date(dateStr)
  const now = new Date()
  const diffTime = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return '今天'
  }
  else if (diffDays === 1) {
    return '昨天'
  }
  else if (diffDays < 7) {
    return `${diffDays}天前`
  }
  else {
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
  }
}

// 新增的交互方法
function showMenu() {
  console.log('显示菜单')
}

function showNotifications() {
  console.log('显示通知')
}

function showProfile() {
  console.log('显示个人资料')
}

function quickSale() {
  console.log('quickSale clicked, showQuickEntry before:', showQuickEntry.value)
  showQuickEntry.value = true
  console.log('quickSale clicked, showQuickEntry after:', showQuickEntry.value)
}

function viewReports() {
  console.log('查看报表')
}

function exportData() {
  console.log('导出数据')
}

function showStatMenu() {
  console.log('显示统计菜单')
}

onMounted(() => {
  salesStore.loadSalesData()
})
</script>

<template>
  <view class="statistics-page">
    <!-- 顶部导航栏 -->
    <view class="top-navbar">
      <view class="navbar-content">
        <view class="nav-left">
          <view class="menu-icon" @click="showMenu">
            <text class="icon-line" />
            <text class="icon-line" />
            <text class="icon-line" />
          </view>
          <view class="nav-title-section">
            <text class="nav-title">
              销售统计
            </text>
            <text class="nav-subtitle">
              数据驱动决策
            </text>
          </view>
        </view>
        <view class="nav-right">
          <view class="notification-btn" @click="showNotifications">
            <text class="notification-icon">
              🔔
            </text>
            <view class="notification-badge">
              3
            </view>
          </view>
          <view class="profile-avatar" @click="showProfile">
            <text class="avatar-text">
              养
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 顶部背景区域 -->
    <view class="header-section">
      <view class="header-background">
        <view class="bg-pattern" />
        <view class="floating-elements">
          <view class="floating-circle circle-1" />
          <view class="floating-circle circle-2" />
          <view class="floating-circle circle-3" />
        </view>
      </view>

      <view class="header-content">
        <view class="welcome-section">
          <view class="greeting">
            <text class="greeting-text">
              你好，养殖户 👋
            </text>
            <text class="greeting-subtitle">
              今天是个收获的好日子
            </text>
          </view>
          <view class="weather-info">
            <view class="weather-item">
              <text class="weather-icon">
                ☀️
              </text>
              <text class="weather-text">
                晴朗 25°C
              </text>
            </view>
            <view class="date-info">
              <text class="current-date">
                {{ formatCurrentDate() }}
              </text>
            </view>
          </view>
        </view>

        <!-- 快速操作按钮 -->
        <view class="quick-actions">
          <view class="action-btn primary" @click="quickSale">
            <text class="action-icon">
              ⚡
            </text>
            <text class="action-text">
              快速销售
            </text>
          </view>
          <view class="action-btn secondary" @click="viewReports">
            <text class="action-icon">
              📊
            </text>
            <text class="action-text">
              查看报表
            </text>
          </view>
          <view class="action-btn tertiary" @click="exportData">
            <text class="action-icon">
              📤
            </text>
            <text class="action-text">
              导出数据
            </text>
          </view>
        </view>
      </view>

      <!-- 主要统计卡片 -->
      <view class="main-stats">
        <view class="main-stat-card revenue">
          <view class="stat-header">
            <view class="stat-icon-wrapper">
              <text class="stat-icon">
                💰
              </text>
            </view>
            <view class="stat-menu" @click="showStatMenu">
              <text class="menu-dots">
                ⋯
              </text>
            </view>
          </view>
          <view class="stat-content">
            <text class="stat-number">
              ¥{{ totalSales.toLocaleString() }}
            </text>
            <text class="stat-label">
              总销售额
            </text>
            <view class="stat-trend">
              <text class="trend-icon">
                📈
              </text>
              <text class="trend-text">
                +12.5%
              </text>
              <text class="trend-period">
                较上月
              </text>
            </view>
          </view>
          <view class="stat-chart">
            <view class="mini-chart">
              <view class="chart-bar" style="height: 20%" />
              <view class="chart-bar" style="height: 40%" />
              <view class="chart-bar" style="height: 60%" />
              <view class="chart-bar" style="height: 80%" />
              <view class="chart-bar" style="height: 100%" />
              <view class="chart-bar" style="height: 70%" />
              <view class="chart-bar" style="height: 90%" />
            </view>
          </view>
        </view>

        <view class="secondary-stats">
          <view class="secondary-stat">
            <view class="secondary-header">
              <view class="secondary-icon-wrapper">
                <text class="secondary-icon">
                  🐰
                </text>
              </view>
              <view class="stat-change positive">
                +8
              </view>
            </view>
            <view class="secondary-content">
              <text class="secondary-number">
                {{ totalQuantity }}
              </text>
              <text class="secondary-label">
                总销售量
              </text>
            </view>
          </view>
          <view class="secondary-stat">
            <view class="secondary-header">
              <view class="secondary-icon-wrapper">
                <text class="secondary-icon">
                  📅
                </text>
              </view>
              <view class="stat-change positive">
                +15%
              </view>
            </view>
            <view class="secondary-content">
              <text class="secondary-number">
                ¥{{ todaySales }}
              </text>
              <text class="secondary-label">
                今日销售
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-section">
      <!-- 最近销售记录 -->
      <view class="recent-sales-card">
        <view class="card-header">
          <view class="card-title">
            <text class="title-icon">
              📋
            </text>
            <text class="title-text">
              最近销售记录
            </text>
          </view>
          <view class="view-all">
            <text class="view-all-text">
              查看全部
            </text>
            <text class="view-all-icon">
              →
            </text>
          </view>
        </view>

        <view class="sales-list">
          <view
            v-for="(sale, index) in recentSales"
            :key="index"
            class="sale-item"
          >
            <view class="sale-left">
              <view class="sale-icon">
                🐰
              </view>
              <view class="sale-info">
                <text class="sale-breed">
                  {{ sale.breed }}
                </text>
                <text class="sale-date">
                  {{ formatDate(sale.date) }}
                </text>
              </view>
            </view>
            <view class="sale-right">
              <view class="sale-amount">
                <text class="sale-total">
                  ¥{{ sale.total.toLocaleString() }}
                </text>
                <text class="sale-quantity">
                  {{ sale.quantity }}只 × ¥{{ sale.price }}
                </text>
              </view>
              <view class="sale-status">
                <text class="status-dot">
                  ●
                </text>
                <text class="status-text">
                  已完成
                </text>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view v-if="recentSales.length === 0" class="empty-state">
            <text class="empty-icon">
              📊
            </text>
            <text class="empty-text">
              暂无销售记录
            </text>
            <text class="empty-subtitle">
              开始记录您的第一笔销售吧
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 快速录入弹窗 -->
    <view v-if="showQuickEntry" class="popup-overlay" @click="showQuickEntry = false">
      <view class="popup-container" @click.stop>
        <view class="quick-entry-popup">
          <view class="popup-header">
            <view class="popup-title">
              <text class="title-icon">
                ⚡
              </text>
              <text class="title-text">
                快速录入
              </text>
            </view>
            <view class="popup-close" @click="showQuickEntry = false">
              <text class="close-icon">
                ✕
              </text>
            </view>
          </view>

          <view class="popup-content">
            <view class="form-grid">
              <view class="form-item">
                <view class="form-label">
                  <text class="label-icon">
                    🐰
                  </text>
                  <text class="label-text">
                    兔子品种
                  </text>
                </view>
                <u-input
                  v-model="entryForm.breed"
                  placeholder="如：新西兰白兔"
                  class="modern-input"
                  border="none"
                  :custom-style="inputStyle"
                />
              </view>

              <view class="form-row">
                <view class="form-item half">
                  <view class="form-label">
                    <text class="label-icon">
                      📊
                    </text>
                    <text class="label-text">
                      数量
                    </text>
                  </view>
                  <u-input
                    v-model="entryForm.quantity"
                    placeholder="只数"
                    type="number"
                    class="modern-input"
                    border="none"
                    :custom-style="inputStyle"
                  />
                </view>

                <view class="form-item half">
                  <view class="form-label">
                    <text class="label-icon">
                      💰
                    </text>
                    <text class="label-text">
                      单价
                    </text>
                  </view>
                  <u-input
                    v-model="entryForm.price"
                    placeholder="元/只"
                    type="number"
                    class="modern-input"
                    border="none"
                    :custom-style="inputStyle"
                  />
                </view>
              </view>

              <view class="form-item">
                <view class="form-label">
                  <text class="label-icon">
                    📅
                  </text>
                  <text class="label-text">
                    销售日期
                  </text>
                </view>
                <view class="date-picker-input" @click="openDatePicker">
                  <text class="date-text">
                    {{ formattedDate }}
                  </text>
                  <text class="date-icon">
                    📅
                  </text>
                </view>
              </view>
            </view>

            <view class="submit-section">
              <u-button
                type="primary"
                class="modern-submit-btn"
                :custom-style="submitBtnStyle"
                @click="submitSale"
              >
                <text class="btn-icon">
                  ✨
                </text>
                <text class="btn-text">
                  提交销售记录
                </text>
              </u-button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 日历组件 -->
    <u-calendar
      v-model:show="showDatePicker"
      :min-date="minDate"
      :max-date="maxDate"
      :monthNum="6"
      :default-date="defaultDate"
      mode="single"
      @confirm="onDateConfirm"
      @close="onDateClose"
    />
  </view>
</template>

<style lang="scss" scoped>
.statistics-page {
  min-height: 100vh;
  background: white;
  padding-bottom: 120rpx;
}

// 顶部导航栏
.top-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  padding: var(--status-bar-height, 44rpx) 0 0;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 40rpx;
  height: 88rpx;
}

.nav-left {
  display: flex;
  align-items: center;

  .menu-icon {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-right: 24rpx;
    cursor: pointer;

    .icon-line {
      width: 24rpx;
      height: 3rpx;
      background: #374151;
      margin: 2rpx 0;
      border-radius: 2rpx;
      transition: all 0.3s ease;
    }

    &:active .icon-line {
      background: #667eea;
    }
  }

  .nav-title-section {
    .nav-title {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #1a202c;
      line-height: 1;
      margin-bottom: 4rpx;
    }

    .nav-subtitle {
      font-size: 20rpx;
      color: #64748b;
    }
  }
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 20rpx;

  .notification-btn {
    position: relative;
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8fafc;
    border-radius: 12rpx;
    cursor: pointer;

    .notification-icon {
      font-size: 24rpx;
    }

    .notification-badge {
      position: absolute;
      top: -4rpx;
      right: -4rpx;
      width: 20rpx;
      height: 20rpx;
      background: #ef4444;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16rpx;
      color: white;
      font-weight: bold;
    }
  }

  .profile-avatar {
    width: 48rpx;
    height: 48rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .avatar-text {
      font-size: 20rpx;
      color: white;
      font-weight: bold;
    }
  }
}

.header-section {
  background: white;
  padding: 40rpx 40rpx 0;
  padding-top: calc(88rpx + var(--status-bar-height, 44rpx));
  position: relative;
  margin-top: 0;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  .bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.6;
  }

  .floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    .floating-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;

      &.circle-1 {
        width: 120rpx;
        height: 120rpx;
        top: 20%;
        left: 10%;
        animation-delay: 0s;
      }

      &.circle-2 {
        width: 80rpx;
        height: 80rpx;
        top: 60%;
        right: 15%;
        animation-delay: 2s;
      }

      &.circle-3 {
        width: 60rpx;
        height: 60rpx;
        bottom: 20%;
        left: 70%;
        animation-delay: 4s;
      }
    }
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20rpx) rotate(180deg);
  }
}

.header-content {
  position: relative;
  z-index: 1;
  margin-bottom: 40rpx;
}

.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 48rpx;
}

.greeting {
  .greeting-text {
    display: block;
    font-size: 40rpx;
    font-weight: bold;
    color: #1a202c;
    margin-bottom: 12rpx;
  }

  .greeting-subtitle {
    font-size: 28rpx;
    color: #64748b;
    font-weight: 500;
  }
}

.weather-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12rpx;

  .weather-item {
    display: flex;
    align-items: center;
    background: #f1f5f9;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;

    .weather-icon {
      margin-right: 8rpx;
      font-size: 20rpx;
    }

    .weather-text {
      font-size: 22rpx;
      color: #64748b;
    }
  }

  .date-info {
    .current-date {
      font-size: 24rpx;
      color: #64748b;
      background: #f1f5f9;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
    }
  }
}

.quick-actions {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;

  .action-btn {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx 16rpx;
    border-radius: 20rpx;
    backdrop-filter: blur(20px);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;

    .action-icon {
      font-size: 32rpx;
      margin-bottom: 8rpx;
    }

    .action-text {
      font-size: 20rpx;
      font-weight: 500;
    }

    &.primary {
      background: #667eea;
      color: white;

      &:active {
        background: #5a67d8;
        transform: translateY(-2rpx);
      }
    }

    &.secondary {
      background: #f1f5f9;
      color: #64748b;

      &:active {
        background: #e2e8f0;
        transform: translateY(-2rpx);
      }
    }

    &.tertiary {
      background: #f8fafc;
      color: #64748b;

      &:active {
        background: #f1f5f9;
        transform: translateY(-2rpx);
      }
    }
  }
}

.main-stats {
  position: relative;
  z-index: 1;
  display: flex;
  gap: 20rpx;
  margin-top: 0;
  margin-bottom: 40rpx;
}

.main-stat-card {
  flex: 2;
  background: white;
  border-radius: 20rpx;
  padding: 28rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;

  .stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .stat-icon-wrapper {
      width: 64rpx;
      height: 64rpx;
      background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4rpx 16rpx rgba(251, 191, 36, 0.3);

      .stat-icon {
        font-size: 32rpx;
      }
    }

    .stat-menu {
      width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8rpx;
      cursor: pointer;

      .menu-dots {
        font-size: 20rpx;
        color: #64748b;
      }

      &:active {
        background: #f1f5f9;
      }
    }
  }

  .stat-content {
    margin-bottom: 24rpx;

    .stat-number {
      font-size: 48rpx;
      font-weight: bold;
      color: #1a202c;
      margin-bottom: 8rpx;
      line-height: 1;
    }

    .stat-label {
      font-size: 26rpx;
      color: #64748b;
      margin-bottom: 16rpx;
    }

    .stat-trend {
      display: flex;
      align-items: center;
      gap: 8rpx;

      .trend-icon {
        font-size: 20rpx;
      }

      .trend-text {
        font-size: 24rpx;
        color: #10b981;
        font-weight: 600;
      }

      .trend-period {
        font-size: 20rpx;
        color: #64748b;
      }
    }
  }

  .stat-chart {
    .mini-chart {
      display: flex;
      align-items: end;
      gap: 4rpx;
      height: 40rpx;

      .chart-bar {
        flex: 1;
        background: linear-gradient(180deg, #10b981 0%, #059669 100%);
        border-radius: 2rpx 2rpx 0 0;
        min-height: 8rpx;
        animation: chartGrow 1.5s ease-out;
      }
    }
  }
}

@keyframes chartGrow {
  from {
    height: 0;
  }
  to {
    height: var(--final-height, 100%);
  }
}

.secondary-stats {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.secondary-stat {
  background: white;
  border-radius: 16rpx;
  padding: 20rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  .secondary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .secondary-icon-wrapper {
      width: 48rpx;
      height: 48rpx;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &:nth-child(1) {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        box-shadow: 0 4rpx 16rpx rgba(139, 92, 246, 0.3);
      }

      .secondary-icon {
        font-size: 24rpx;
      }
    }

    .stat-change {
      font-size: 18rpx;
      font-weight: 600;
      padding: 4rpx 8rpx;
      border-radius: 8rpx;

      &.positive {
        color: #10b981;
        background: rgba(16, 185, 129, 0.1);
      }

      &.negative {
        color: #ef4444;
        background: rgba(239, 68, 68, 0.1);
      }
    }
  }

  .secondary-content {
    .secondary-number {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #1a202c;
      margin-bottom: 4rpx;
    }

    .secondary-label {
      font-size: 22rpx;
      color: #64748b;
    }
  }

  // 为第二个卡片设置不同的图标背景色
  &:nth-child(2) .secondary-header .secondary-icon-wrapper {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    box-shadow: 0 4rpx 16rpx rgba(6, 182, 212, 0.3);
  }
}

.content-section {
  padding: 0 40rpx 40rpx;
  background: white;
}

.quick-entry-card,
.recent-sales-card {
  background: white;
  border-radius: 20rpx;
  padding: 28rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  margin-bottom: 24rpx;
  transition: all 0.3s ease;
  margin-bottom: 24rpx;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(-1rpx);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  }
}

.card-header {
  margin-bottom: 32rpx;

  .card-title {
    display: flex;
    align-items: center;
    margin-bottom: 8rpx;

    .title-icon {
      font-size: 32rpx;
      margin-right: 12rpx;
    }

    .title-text {
      font-size: 32rpx;
      font-weight: bold;
      color: #1a202c;
    }
  }

  .card-subtitle {
    font-size: 24rpx;
    color: #64748b;
    margin-left: 44rpx;
  }

  .view-all {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .view-all-text {
      font-size: 24rpx;
      color: #667eea;
      margin-right: 8rpx;
    }

    .view-all-icon {
      font-size: 20rpx;
      color: #667eea;
    }
  }
}

.form-grid {
  .form-item {
    margin-bottom: 32rpx;

    &.half {
      flex: 1;
    }
  }

  .form-row {
    display: flex;
    gap: 20rpx;
  }

  .form-label {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;

    .label-icon {
      font-size: 24rpx;
      margin-right: 8rpx;
    }

    .label-text {
      font-size: 26rpx;
      font-weight: 600;
      color: #374151;
    }
  }
}

.submit-section {
  margin-top: 40rpx;

  .modern-submit-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .btn-icon {
      margin-right: 8rpx;
      font-size: 24rpx;
    }

    .btn-text {
      font-size: 28rpx;
      font-weight: bold;
    }
  }
}

.sales-list {
  .sale-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 0;
    border-bottom: 1rpx solid #f1f5f9;
    transition: all 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background: #f8fafc;
      border-radius: 12rpx;
      margin: 0 -16rpx;
      padding: 24rpx 16rpx;
    }
  }

  .sale-left {
    display: flex;
    align-items: center;
    flex: 1;

    .sale-icon {
      font-size: 40rpx;
      margin-right: 20rpx;
      background: #f0f9ff;
      width: 80rpx;
      height: 80rpx;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .sale-info {
      .sale-breed {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: #1a202c;
        margin-bottom: 8rpx;
      }

      .sale-date {
        font-size: 22rpx;
        color: #64748b;
      }
    }
  }

  .sale-right {
    text-align: right;

    .sale-amount {
      margin-bottom: 8rpx;

      .sale-total {
        display: block;
        font-size: 28rpx;
        font-weight: bold;
        color: #059669;
        margin-bottom: 4rpx;
      }

      .sale-quantity {
        font-size: 20rpx;
        color: #64748b;
      }
    }

    .sale-status {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .status-dot {
        font-size: 12rpx;
        color: #10b981;
        margin-right: 8rpx;
      }

      .status-text {
        font-size: 20rpx;
        color: #10b981;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;

  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 24rpx;
    display: block;
  }

  .empty-text {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #64748b;
    margin-bottom: 12rpx;
  }

  .empty-subtitle {
    font-size: 24rpx;
    color: #94a3b8;
  }
}

// 弹窗遮罩层
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.popup-container {
  width: 100%;
  max-width: 750rpx;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

// 快速录入弹窗样式
.quick-entry-popup {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);

  .popup-title {
    display: flex;
    align-items: center;
    gap: 12rpx;

    .title-icon {
      font-size: 32rpx;
    }

    .title-text {
      font-size: 32rpx;
      font-weight: bold;
      color: #1a202c;
    }
  }

  .popup-close {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f1f5f9;
    border-radius: 50%;
    cursor: pointer;

    .close-icon {
      font-size: 24rpx;
      color: #64748b;
    }

    &:active {
      background: #e2e8f0;
      transform: scale(0.95);
    }
  }
}

.popup-content {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;

  .form-grid {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
  }

  .form-row {
    display: flex;
    gap: 20rpx;
  }

  .form-item {
    display: flex;
    flex-direction: column;
    gap: 12rpx;

    &.half {
      flex: 1;
    }
  }

  .form-label {
    display: flex;
    align-items: center;
    gap: 8rpx;

    .label-icon {
      font-size: 24rpx;
    }

    .label-text {
      font-size: 28rpx;
      font-weight: 500;
      color: #374151;
    }
  }

  .submit-section {
    margin-top: 40rpx;
    padding-top: 24rpx;
    border-top: 1rpx solid rgba(0, 0, 0, 0.06);

    .modern-submit-btn {
      width: 100%;
      height: 96rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12rpx;
      border-radius: 20rpx;
      font-weight: bold;

      .btn-icon {
        font-size: 24rpx;
      }

      .btn-text {
        font-size: 32rpx;
      }
    }
  }

  // 日期选择器输入框样式
  .date-picker-input {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
    border-radius: 12rpx;
    padding: 32rpx;
    border: 2rpx solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;

    .date-text {
      font-size: 32rpx;
      color: #374151;
      font-weight: 500;
    }

    .date-icon {
      font-size: 32rpx;
      opacity: 0.6;
    }

    &:active {
      background: #e9ecef;
      border-color: #667eea;
      transform: scale(0.98);
    }
  }
}
</style>
