<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'

// Tab 页面映射
const pages = {
  statistics: '/pages/statistics/statistics',
  profile: '/pages/profile/profile',
}

// 需要显示tabBar的页面路径
const tabBarPages = [
  '/pages/statistics/statistics',
  '/pages/profile/profile',
]

// 当前激活的标签
const activeTab = ref('statistics')

// 判断当前页面是否需要显示tabBar
const showTabBar = computed(() => {
  try {
    const currentPages = getCurrentPages()
    if (currentPages.length === 0)
      return false

    const currentPage = currentPages[currentPages.length - 1]
    const currentRoute = `/${currentPage.route}`

    return tabBarPages.includes(currentRoute)
  }
  catch (error) {
    console.warn('获取当前页面路径失败:', error)
    return false
  }
})

// 根据当前页面路径初始化激活标签
onMounted(() => {
  const currentPage = getCurrentPages().pop()
  if (currentPage) {
    const route = `/${currentPage.route}`
    for (const [tab, path] of Object.entries(pages)) {
      if (route === path) {
        activeTab.value = tab
        break
      }
    }
  }
})

// 导航到指定页面
function navigateTo(url: string, tab: string) {
  if (activeTab.value !== tab) {
    activeTab.value = tab
    uni.navigateTo({
      url,
      fail: (err) => {
        console.error('页面跳转失败:', err)
      },
    })
  }
}

// 模拟用户角色（可以从store获取）
const userRole = ref('admin') // admin, manager, user

// 根据用户角色过滤可显示的tabs
const availableTabs = computed(() => {
  const allTabs = [
    {
      key: 'statistics',
      label: '统计',
      icon: '📊',
      path: '/pages/statistics/statistics',
      roles: ['admin', 'manager', 'user'],
    },
    {
      key: 'profile',
      label: '我的',
      icon: '👤',
      path: '/pages/profile/profile',
      roles: ['admin', 'manager', 'user'],
    },
    {
      key: 'admin',
      label: '管理',
      icon: '⚙️',
      path: '/pages/admin/admin',
      roles: ['admin'],
    },
    {
      key: 'reports',
      label: '报表',
      icon: '📈',
      path: '/pages/reports/reports',
      roles: ['admin', 'manager'],
    },
  ]

  return allTabs.filter(tab => tab.roles.includes(userRole.value))
})
</script>

<template>
  <view class="layout-container" :class="{ 'has-tabbar': showTabBar }">
    <!-- 页面内容插槽 -->
    <slot />

    <!-- 自定义底部 Tabbar - 只在指定页面显示 -->
    <view v-if="showTabBar" class="custom-tabbar">
      <view
        v-for="tab in availableTabs"
        :key="tab.key"
        class="tabbar-item"
        :class="{ 'tabbar-active': activeTab === tab.key }"
        @tap="navigateTo(tab.path, tab.key)"
      >
        <view class="tabbar-icon">
          <text class="emoji">
            {{ tab.icon }}
          </text>
        </view>
        <text class="tabbar-text">
          {{ tab.label }}
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.layout-container {
  width: 100%;
  min-height: 100vh;

  &.has-tabbar {
    padding-bottom: 100rpx; /* 只有在显示tabbar时才添加底部间距 */
  }
}

.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  align-items: center;
  z-index: 9999;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

  .tabbar-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10rpx 0;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }

    &.tabbar-active {
      .tabbar-icon .emoji {
        transform: scale(1.1);
      }

      .tabbar-text {
        color: #018d71;
        font-weight: bold;
      }
    }

    .tabbar-icon {
      margin-bottom: 4rpx;

      .emoji {
        font-size: 44rpx;
        line-height: 1;
        transition: transform 0.3s ease;
      }
    }

    .tabbar-text {
      font-size: 20rpx;
      color: #7d7e80;
      transition: color 0.3s ease;
    }
  }
}
</style>
