# API 使用指南

## 🚀 快速开始

### 基础信息
- **API 基础URL**: `http://localhost:8000`
- **API 版本**: `v1`
- **API 前缀**: `/api/v1`

### 认证方式
使用 JWT Bearer Token 认证

## 📱 手机号登录系统（带验证码防护）

### 1. 获取验证码

**接口**: `GET /api/v1/auth/captcha`

**响应**:
```json
{
  "captcha_id": "d410e4d329a79f9df5fc750e1b4edde1",
  "captcha_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgA...",
  "expires_in": 300
}
```

**说明**:
- `captcha_id`: 验证码唯一标识
- `captcha_image`: base64编码的验证码图片
- `expires_in`: 验证码有效期（秒）

### 2. 查询登录尝试状态

**接口**: `GET /api/v1/auth/login-attempts/{phone}`

**响应**:
```json
{
  "remaining_attempts": 5,
  "is_locked": false,
  "locked_until": null,
  "requires_captcha": false
}
```

**说明**:
- `remaining_attempts`: 剩余尝试次数
- `is_locked`: 是否被锁定
- `locked_until`: 锁定到期时间
- `requires_captcha`: 是否需要验证码

### 3. 用户登录

**接口**: `POST /api/v1/auth/login`

**普通登录请求体**:
```json
{
  "phone": "13800138000",
  "password": "admin123"
}
```

**带验证码登录请求体**:
```json
{
  "phone": "13800138000",
  "password": "admin123",
  "captcha_id": "d410e4d329a79f9df5fc750e1b4edde1",
  "captcha_code": "ABCD"
}
```

**响应**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

**验证码触发规则**:
- 前3次登录失败：不需要验证码
- 剩余2次或更少：需要验证码
- 5次失败后：账户锁定15分钟

**手机号格式要求**:
- 必须是11位中国大陆手机号
- 格式：1[3-9]xxxxxxxxx

### 4. 用户退出登录

**接口**: `POST /api/v1/auth/logout`

**请求头**:
```
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "message": "退出登录成功",
  "logged_out_at": "2024-01-01T12:00:00.000000"
}
```

**功能说明**:
- 将当前token加入黑名单
- 退出后token立即失效
- 不影响其他用户的token
- 防止重复退出登录

### 2. 获取当前用户信息

**接口**: `GET /api/v1/users/me`

**请求头**:
```
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "id": 1,
  "phone": "13800138000",
  "username": "admin",
  "is_active": true,
  "is_superuser": true,
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00"
}
```

### 3. 更新用户信息

**接口**: `PUT /api/v1/users/me`

**请求头**:
```
Authorization: Bearer <access_token>
```

**请求体**:
```json
{
  "phone": "13900139000",
  "username": "new_username",
  "password": "new_password"
}
```

## 👥 用户管理（管理员功能）

### 1. 获取用户列表

**接口**: `GET /api/v1/users/`

**权限**: 需要管理员权限

**请求头**:
```
Authorization: Bearer <admin_access_token>
```

**查询参数**:
- `skip`: 跳过的记录数（默认：0）
- `limit`: 返回的记录数（默认：100）

### 2. 创建新用户

**接口**: `POST /api/v1/users/`

**权限**: 需要管理员权限

**请求体**:
```json
{
  "phone": "15000150000",
  "username": "newuser",
  "password": "password123",
  "is_active": true,
  "is_superuser": false
}
```

### 3. 获取指定用户信息

**接口**: `GET /api/v1/users/{user_id}`

**权限**: 管理员或用户本人

### 4. 更新指定用户信息

**接口**: `PUT /api/v1/users/{user_id}`

**权限**: 需要管理员权限

## 🔧 系统接口

### 1. 健康检查

**接口**: `GET /health`

**响应**:
```json
{
  "status": "healthy"
}
```

### 2. 根接口

**接口**: `GET /`

**响应**:
```json
{
  "message": "Welcome to FastAPI Backend"
}
```

## 📋 测试账户

### 管理员账户
- **手机号**: `13800138000`
- **用户名**: `admin`
- **密码**: `admin123`
- **权限**: 超级用户

### 演示账户
- **手机号**: `13900139000`
- **用户名**: `demo`
- **密码**: `demo123`
- **权限**: 普通用户

### 测试账户
- **手机号**: `15000150000`
- **用户名**: `test`
- **密码**: `test123`
- **权限**: 普通用户

## 🌐 API 文档

### Swagger UI
访问 `http://localhost:8000/docs` 查看交互式API文档

### ReDoc
访问 `http://localhost:8000/redoc` 查看API文档

## 📝 使用示例

### Python 示例

```python
import requests

# 1. 登录获取token
login_response = requests.post(
    "http://localhost:8000/api/v1/auth/login",
    json={
        "phone": "13800138000",
        "password": "admin123"
    }
)

token = login_response.json()["access_token"]

# 2. 使用token访问受保护的接口
headers = {"Authorization": f"Bearer {token}"}

user_response = requests.get(
    "http://localhost:8000/api/v1/users/me",
    headers=headers
)

user_info = user_response.json()
print(f"当前用户: {user_info['username']} ({user_info['phone']})")
```

### JavaScript 示例

```javascript
// 1. 登录获取token
const loginResponse = await fetch('http://localhost:8000/api/v1/auth/login', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        phone: '13800138000',
        password: 'admin123'
    })
});

const { access_token } = await loginResponse.json();

// 2. 使用token访问受保护的接口
const userResponse = await fetch('http://localhost:8000/api/v1/users/me', {
    headers: {
        'Authorization': `Bearer ${access_token}`
    }
});

const userInfo = await userResponse.json();
console.log(`当前用户: ${userInfo.username} (${userInfo.phone})`);
```

## 🌐 CORS 跨域配置

### 支持的源

API已配置完善的CORS支持，允许以下源的跨域请求：

**开发环境端口**:
- `http://localhost:3000` (React默认)
- `http://localhost:8080` (Vue默认)
- `http://localhost:5173` (Vite默认)
- `http://localhost:4200` (Angular默认)
- `http://127.0.0.1:3000`
- `http://127.0.0.1:8080`
- `http://127.0.0.1:5173`
- `https://localhost:*` (HTTPS版本)

### CORS 配置信息

**接口**: `GET /cors-info`

**响应**:
```json
{
  "allowed_origins": ["http://localhost:3000", "..."],
  "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"],
  "allowed_headers": ["Accept", "Authorization", "Content-Type", "..."],
  "allow_credentials": true,
  "max_age": 86400
}
```

### 前端请求配置

**JavaScript/Axios**:
```javascript
// 配置基础URL和凭据
axios.defaults.baseURL = 'http://localhost:8000';
axios.defaults.withCredentials = true;

// 设置认证头
axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
```

**Fetch API**:
```javascript
fetch('http://localhost:8000/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Origin': window.location.origin
  },
  credentials: 'include',
  body: JSON.stringify(loginData)
});
```

## 🎨 前端集成示例

### 1. 原生JavaScript

参考文件: `frontend-examples/vanilla-js/index.html`

完整的HTML页面，包含：
- 手机号登录表单
- 验证码显示和刷新
- 登录状态检查
- 用户信息显示
- 完整的错误处理

### 2. React组件

参考文件: `frontend-examples/react/LoginComponent.jsx`

React函数组件，特性：
- 使用Hooks管理状态
- Axios HTTP客户端
- 响应式UI设计
- 自动token管理

### 3. Vue.js组件

参考文件: `frontend-examples/vue/LoginComponent.vue`

Vue单文件组件，包含：
- 响应式数据绑定
- 组件生命周期管理
- 样式封装
- 事件处理

## ❌ 错误处理

### 常见错误码

- **400**: 请求参数错误（如手机号或密码错误）
- **401**: 未授权（token无效或过期）
- **403**: 权限不足
- **422**: 数据验证失败（如手机号格式错误）
- **423**: 账户被锁定
- **500**: 服务器内部错误

### 错误响应格式

```json
{
  "detail": "错误描述信息"
}
```

### CORS错误处理

如果遇到CORS错误：

1. **检查端口**: 确保前端运行在支持的端口
2. **检查协议**: HTTP/HTTPS协议要匹配
3. **检查Origin**: 确保请求头包含正确的Origin
4. **预检请求**: 复杂请求会先发送OPTIONS预检请求

## 🔒 安全注意事项

1. **Token 安全**: 请妥善保管访问令牌，不要在客户端明文存储
2. **HTTPS**: 生产环境请使用HTTPS协议
3. **密码强度**: 建议使用强密码
4. **Token 过期**: 默认token有效期为30分钟，过期后需重新登录
5. **验证码保护**: 多次登录失败会触发验证码和账户锁定
6. **CORS配置**: 生产环境请限制允许的源域名
