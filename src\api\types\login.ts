/**
 * 用户信息
 */
export interface IUserInfoVo {
  id: number
  phone: string
  username: string
  avatar?: string
  is_active: boolean
  is_superuser: boolean
  created_at: string
  updated_at: string
  token?: string // 添加token字段，用于拦截器
}

/**
 * 登录返回的信息
 */
export interface IUserLogin {
  access_token: string
  token_type: string
}

/**
 * 验证码信息
 */
export interface ICaptcha {
  captcha_id: string
  captcha_image: string
  expires_in: number
}

/**
 * 登录尝试状态
 */
export interface ILoginAttempts {
  remaining_attempts: number
  is_locked: boolean
  locked_until: string | null
  requires_captcha: boolean
}


/**
 * 上传成功的信息
 */
export interface IUploadSuccessInfo {
  fileId: number
  originalName: string
  fileName: string
  storagePath: string
  fileHash: string
  fileType: string
  fileBusinessType: string
  fileSize: number
}
/**
 * 更新用户信息
 */
export interface IUpdateInfo {
  id: number
  name: string
  sex: string
}
/**
 * 更新用户信息
 */
export interface IUpdatePassword {
  id: number
  oldPassword: string
  newPassword: string
  confirmPassword: string
}
