/**
 * 路由拦截，登录拦截
 * 使用白名单机制 - 所有页面都需要登录，除了登录页面和其他白名单页面
 */
import { useUserStore } from '@/store'
import { getLastPage } from '@/utils'

const loginRoute = import.meta.env.VITE_LOGIN_URL || '/pages/login/login'

// 白名单页面 - 不需要登录就可以访问的页面
const whiteListPages = [
  '/pages/login/login',
  // 可以在这里添加其他不需要登录的页面
]

function isLogined() {
  const userStore = useUserStore()
  return userStore.isLoggedIn()
}

// 白名单登录拦截器 - （所有页面都需要登录，除了白名单页面）
const navigateToInterceptor = {
  // 注意，这里的url是 '/' 开头的，如 '/pages/index/index'，跟 'pages.json' 里面的 path 不同
  // 增加对相对路径的处理
  invoke({ url }: { url: string }) {
    console.log('路由拦截 - 目标URL:', url)
    let path = url.split('?')[0]

    // 处理相对路径
    if (!path.startsWith('/')) {
      const currentPath = getLastPage().route
      const normalizedCurrentPath = currentPath.startsWith('/') ? currentPath : `/${currentPath}`
      const baseDir = normalizedCurrentPath.substring(0, normalizedCurrentPath.lastIndexOf('/'))
      path = `${baseDir}/${path}`
    }

    console.log('路由拦截 - 处理后的路径:', path)

    // 检查是否在白名单中
    const isInWhiteList = whiteListPages.includes(path)
    if (isInWhiteList) {
      console.log('路由拦截 - 白名单页面，允许访问')
      return true
    }

    // 检查是否已登录
    const hasLogin = isLogined()
    if (hasLogin) {
      console.log('路由拦截 - 已登录，允许访问')
      return true
    }

    // 未登录且不在白名单中，跳转到登录页
    console.log('路由拦截 - 未登录，跳转到登录页')
    const redirectRoute = `${loginRoute}?redirect=${encodeURIComponent(url)}`
    uni.navigateTo({ url: redirectRoute })
    return false
  },
}

export const routeInterceptor = {
  install() {
    uni.addInterceptor('navigateTo', navigateToInterceptor)
    uni.addInterceptor('reLaunch', navigateToInterceptor)
    uni.addInterceptor('redirectTo', navigateToInterceptor)
    uni.addInterceptor('switchTab', navigateToInterceptor)
  },
}
