<route lang="json">
{
  "type": "page",
  "layout": "default",
  "style": {
    "navigationBarTitleText": "我的"
  }
}
</route>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'

import { usePageAuth } from '@/hooks/usePageAuth'
import { useUserStore } from '@/store/user'

// 页面认证检查
usePageAuth()

// 使用用户store
const userStore = useUserStore()

// 计算属性：从store中获取用户信息并映射到显示字段
const userInfo = computed(() => {
  const storeUserInfo = userStore.userInfo
  return {
    name: storeUserInfo.username || '用户',
    role: '养殖场主', // 这个可以根据实际业务逻辑设置
    farmName: '兔场管理', // 这个可以根据实际业务逻辑设置
    avatar: storeUserInfo.avatar || '/static/images/default-avatar.png',
    phone: storeUserInfo.phone || '',
    id: storeUserInfo.id || 0,
  }
})

// 弹窗控制
const showCustomerPopup = ref(false)
const showBreedPopup = ref(false)

// 客户管理相关数据
const customerForm = ref({
  name: '',
  phone: '',
  address: '',
  wechat: '',
})

const customers = ref([
  {
    name: '李老板',
    phone: '13800138001',
    address: '北京市朝阳区',
    wechat: 'liboss2024',
  },
  {
    name: '王经理',
    phone: '13800138002',
    address: '上海市浦东新区',
    wechat: '',
  },
])

// 品种管理相关数据
const breedForm = ref({
  name: '',
  description: '',
})

const breeds = ref([
  {
    name: '新西兰白兔',
    description: '生长快速，肉质鲜美，适合商业养殖',
  },
  {
    name: '比利时兔',
    description: '体型较大，繁殖能力强，抗病性好',
  },
])

// 样式配置
const inputStyle = {
  backgroundColor: '#f8f9fa',
  borderRadius: '12px',
  padding: '16px',
  fontSize: '16px',
}

const submitBtnStyle = {
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  borderRadius: '16px',
  height: '56px',
  fontSize: '16px',
  fontWeight: 'bold',
}

// 方法
function editProfile() {
  uni.navigateTo({
    url: '/pages/profile/edit',
  })
}

function navigateTo(_url: string) {
  uni.showToast({
    title: '功能开发中',
    icon: 'none',
  })
  // uni.navigateTo({ url })
}

function logout() {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        userStore.logout()
        uni.reLaunch({
          url: '/pages/login/login',
        })
      }
    },
  })
}

// 新增的交互方法
function showMenu() {
  console.log('显示菜单')
}

function showNotifications() {
  console.log('显示通知')
}

function showProfile() {
  console.log('显示个人资料')
}

// 客户管理方法
function showCustomerManagement() {
  console.log('showCustomerManagement called')
  showCustomerPopup.value = true
}

function addCustomer() {
  if (!customerForm.value.name || !customerForm.value.phone) {
    uni.showToast({
      title: '请填写客户姓名和电话',
      icon: 'none',
    })
    return
  }

  customers.value.push({
    name: customerForm.value.name,
    phone: customerForm.value.phone,
    address: customerForm.value.address,
    wechat: customerForm.value.wechat,
  })

  // 重置表单
  customerForm.value = {
    name: '',
    phone: '',
    address: '',
    wechat: '',
  }

  // 临时隐藏弹出页面，确保对话框能正确显示在最上层
  showCustomerPopup.value = false

  // 延迟显示对话框，确保弹出页面完全隐藏
  setTimeout(() => {
    uni.showModal({
      title: '添加成功',
      content: '客户添加成功！是否继续添加其他客户？',
      confirmText: '继续添加',
      cancelText: '关闭',
      success: (res) => {
        if (res.confirm) {
          // 用户选择继续添加，重新显示弹出页面
          showCustomerPopup.value = true
        }
        // 如果用户选择关闭，保持弹出页面关闭状态
      },
    })
  }, 100)
}

function editCustomer(index: number) {
  const customer = customers.value[index]
  customerForm.value = { ...customer }

  // 删除原客户，等待用户重新添加
  customers.value.splice(index, 1)

  uni.showToast({
    title: '请修改后重新添加',
    icon: 'none',
  })
}

function deleteCustomer(index: number) {
  // 临时隐藏弹出页面，确保对话框能正确显示在最上层
  const wasPopupVisible = showCustomerPopup.value
  showCustomerPopup.value = false

  setTimeout(() => {
    uni.showModal({
      title: '确认删除',
      content: '确定要删除这个客户吗？',
      success: (res) => {
        if (res.confirm) {
          customers.value.splice(index, 1)
          uni.showToast({
            title: '删除成功',
            icon: 'success',
          })
        }
        // 恢复弹出页面显示状态
        if (wasPopupVisible) {
          showCustomerPopup.value = true
        }
      },
      fail: () => {
        // 如果对话框显示失败，也要恢复弹出页面状态
        if (wasPopupVisible) {
          showCustomerPopup.value = true
        }
      },
    })
  }, 100)
}

// 品种管理方法
function showBreedManagement() {
  console.log('showBreedManagement called')
  showBreedPopup.value = true
}

function addBreed() {
  if (!breedForm.value.name) {
    uni.showToast({
      title: '请填写品种名称',
      icon: 'none',
    })
    return
  }

  breeds.value.push({
    name: breedForm.value.name,
    description: breedForm.value.description,
  })

  // 重置表单
  breedForm.value = {
    name: '',
    description: '',
  }

  // 临时隐藏弹出页面，确保对话框能正确显示在最上层
  showBreedPopup.value = false

  // 延迟显示对话框，确保弹出页面完全隐藏
  setTimeout(() => {
    uni.showModal({
      title: '添加成功',
      content: '品种添加成功！是否继续添加其他品种？',
      confirmText: '继续添加',
      cancelText: '关闭',
      success: (res) => {
        if (res.confirm) {
          // 用户选择继续添加，重新显示弹出页面
          showBreedPopup.value = true
        }
        // 如果用户选择关闭，保持弹出页面关闭状态
      },
    })
  }, 100)
}

function editBreed(index: number) {
  const breed = breeds.value[index]
  breedForm.value = { ...breed }

  // 删除原品种，等待用户重新添加
  breeds.value.splice(index, 1)

  uni.showToast({
    title: '请修改后重新添加',
    icon: 'none',
  })
}

function deleteBreed(index: number) {
  // 临时隐藏弹出页面，确保对话框能正确显示在最上层
  const wasPopupVisible = showBreedPopup.value
  showBreedPopup.value = false

  setTimeout(() => {
    uni.showModal({
      title: '确认删除',
      content: '确定要删除这个品种吗？',
      success: (res) => {
        if (res.confirm) {
          breeds.value.splice(index, 1)
          uni.showToast({
            title: '删除成功',
            icon: 'success',
          })
        }
        // 恢复弹出页面显示状态
        if (wasPopupVisible) {
          showBreedPopup.value = true
        }
      },
      fail: () => {
        // 如果对话框显示失败，也要恢复弹出页面状态
        if (wasPopupVisible) {
          showBreedPopup.value = true
        }
      },
    })
  }, 100)
}

onMounted(async () => {
  // 加载用户信息
  try {
    // 如果已经有用户信息，就不需要重新获取
    if (!userStore.userInfo.username) {
      console.log('获取用户信息...')
      await userStore.getUserInfo()
    }
    console.log('当前用户信息:', userStore.userInfo)
  }
  catch (error) {
    console.error('获取用户信息失败:', error)
  }
})
</script>

<template>
  <view class="profile-page">
    <!-- 顶部导航栏 -->
    <view class="top-navbar">
      <view class="navbar-content">
        <view class="nav-left">
          <view class="menu-icon" @click="showMenu">
            <text class="icon-line" />
            <text class="icon-line" />
            <text class="icon-line" />
          </view>
          <view class="nav-title-section">
            <text class="nav-title">
              个人中心
            </text>
            <text class="nav-subtitle">
              管理您的账户信息
            </text>
          </view>
        </view>
        <view class="nav-right">
          <view class="notification-btn" @click="showNotifications">
            <text class="notification-icon">
              🔔
            </text>
            <view class="notification-badge">
              2
            </view>
          </view>
          <view class="profile-avatar-small" @click="showProfile">
            <text class="avatar-text">
              {{ userInfo.name.charAt(0) }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 顶部背景区域 -->
    <view class="header-section">
      <view class="header-background">
        <view class="bg-pattern" />
        <view class="floating-elements">
          <view class="floating-circle circle-1" />
          <view class="floating-circle circle-2" />
          <view class="floating-circle circle-3" />
        </view>
      </view>

      <view class="header-content">
        <!-- 用户信息卡片 -->
        <view class="user-info-card">
          <view class="user-avatar-section">
            <view class="user-avatar">
              <text class="avatar-text-large">
                {{ userInfo.name.charAt(0) }}
              </text>
            </view>
            <view class="edit-btn" @click="editProfile">
              <text class="edit-icon">
                ✏️
              </text>
            </view>
          </view>
          <view class="user-details">
            <text class="user-name">
              {{ userInfo.name }}
            </text>
            <text class="user-role">
              {{ userInfo.role }}
            </text>
            <view class="farm-info">
              <text class="farm-icon">
                🏡
              </text>
              <text class="farm-name">
                {{ userInfo.farmName }}
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-section">
      <!-- 功能菜单 -->
      <view class="menu-card">
        <view class="card-header">
          <view class="card-title">
            <text class="title-icon">
              🛠️
            </text>
            <text class="title-text">
              功能管理
            </text>
          </view>
        </view>
        <view class="menu-grid">
          <view class="menu-item" @click="showCustomerManagement">
            <view class="menu-icon-wrapper customer">
              <text class="menu-icon">
                👥
              </text>
            </view>
            <text class="menu-text">
              客户管理
            </text>
            <view class="menu-arrow">
              <text class="arrow-icon">
                →
              </text>
            </view>
          </view>

          <view class="menu-item" @click="showBreedManagement">
            <view class="menu-icon-wrapper breed">
              <text class="menu-icon">
                🐰
              </text>
            </view>
            <text class="menu-text">
              品种管理
            </text>
            <view class="menu-arrow">
              <text class="arrow-icon">
                →
              </text>
            </view>
          </view>

          <view class="menu-item" @click="navigateTo('/pages/feed/feed')">
            <view class="menu-icon-wrapper feed">
              <text class="menu-icon">
                🌾
              </text>
            </view>
            <text class="menu-text">
              饲料管理
            </text>
            <view class="menu-arrow">
              <text class="arrow-icon">
                →
              </text>
            </view>
          </view>

          <view class="menu-item" @click="navigateTo('/pages/reports/reports')">
            <view class="menu-icon-wrapper reports">
              <text class="menu-icon">
                📊
              </text>
            </view>
            <text class="menu-text">
              报表分析
            </text>
            <view class="menu-arrow">
              <text class="arrow-icon">
                →
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 设置菜单 -->
      <view class="menu-card">
        <view class="card-header">
          <view class="card-title">
            <text class="title-icon">
              ⚙️
            </text>
            <text class="title-text">
              系统设置
            </text>
          </view>
        </view>
        <view class="settings-list">
          <view class="setting-item" @click="navigateTo('/pages/settings/settings')">
            <view class="setting-left">
              <view class="setting-icon-wrapper settings">
                <text class="setting-icon">
                  ⚙️
                </text>
              </view>
              <view class="setting-info">
                <text class="setting-title">
                  系统设置
                </text>
                <text class="setting-desc">
                  个性化配置
                </text>
              </view>
            </view>
            <view class="setting-arrow">
              <text class="arrow-icon">
                →
              </text>
            </view>
          </view>

          <view class="setting-item" @click="navigateTo('/pages/help/help')">
            <view class="setting-left">
              <view class="setting-icon-wrapper help">
                <text class="setting-icon">
                  ❓
                </text>
              </view>
              <view class="setting-info">
                <text class="setting-title">
                  帮助中心
                </text>
                <text class="setting-desc">
                  使用指南与FAQ
                </text>
              </view>
            </view>
            <view class="setting-arrow">
              <text class="arrow-icon">
                →
              </text>
            </view>
          </view>

          <view class="setting-item" @click="navigateTo('/pages/about/about')">
            <view class="setting-left">
              <view class="setting-icon-wrapper about">
                <text class="setting-icon">
                  ℹ️
                </text>
              </view>
              <view class="setting-info">
                <text class="setting-title">
                  关于我们
                </text>
                <text class="setting-desc">
                  版本信息与联系方式
                </text>
              </view>
            </view>
            <view class="setting-arrow">
              <text class="arrow-icon">
                →
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 退出登录 -->
      <view class="logout-card">
        <view class="logout-btn" @click="logout">
          <text class="logout-icon">
            🚪
          </text>
          <text class="logout-text">
            退出登录
          </text>
        </view>
      </view>
    </view>

    <!-- 客户管理弹窗 -->
    <view v-if="showCustomerPopup" class="popup-overlay" @click="showCustomerPopup = false">
      <view class="popup-container" @click.stop>
        <view class="management-popup">
          <view class="popup-header">
            <view class="popup-title">
              <text class="title-icon">
                👥
              </text>
              <text class="title-text">
                客户管理
              </text>
            </view>
            <view class="popup-close" @click="showCustomerPopup = false">
              <text class="close-icon">
                ✕
              </text>
            </view>
          </view>

          <view class="popup-content">
            <!-- 添加客户表单 -->
            <view class="add-form">
              <view class="form-title">
                <text class="form-title-text">
                  添加新客户
                </text>
              </view>
              <view class="form-grid">
                <view class="form-item required">
                  <view class="form-label">
                    <text class="label-icon">
                      👤
                    </text>
                    <text class="label-text">
                      客户姓名
                    </text>
                    <text class="required-mark">
                      *
                    </text>
                  </view>
                  <u-input
                    v-model="customerForm.name"
                    placeholder="请输入客户姓名"
                    class="modern-input"
                    border="none"
                    :custom-style="inputStyle"
                  />
                </view>

                <view class="form-item required">
                  <view class="form-label">
                    <text class="label-icon">
                      📱
                    </text>
                    <text class="label-text">
                      联系电话
                    </text>
                    <text class="required-mark">
                      *
                    </text>
                  </view>
                  <u-input
                    v-model="customerForm.phone"
                    placeholder="请输入联系电话"
                    type="number"
                    class="modern-input"
                    border="none"
                    :custom-style="inputStyle"
                  />
                </view>

                <view class="form-item">
                  <view class="form-label">
                    <text class="label-icon">
                      💬
                    </text>
                    <text class="label-text">
                      微信号
                    </text>
                    <text class="optional-mark">
                      (选填)
                    </text>
                  </view>
                  <u-input
                    v-model="customerForm.wechat"
                    placeholder="请输入微信号"
                    class="modern-input"
                    border="none"
                    :custom-style="inputStyle"
                  />
                </view>

                <view class="form-item">
                  <view class="form-label">
                    <text class="label-icon">
                      📍
                    </text>
                    <text class="label-text">
                      客户地址
                    </text>
                    <text class="optional-mark">
                      (选填)
                    </text>
                  </view>
                  <u-input
                    v-model="customerForm.address"
                    placeholder="请输入客户地址"
                    class="modern-input"
                    border="none"
                    :custom-style="inputStyle"
                  />
                </view>
              </view>

              <view class="submit-section">
                <u-button
                  type="primary"
                  class="modern-submit-btn"
                  :custom-style="submitBtnStyle"
                  @click="addCustomer"
                >
                  <text class="btn-icon">
                    ✨
                  </text>
                  <text class="btn-text">
                    添加客户
                  </text>
                </u-button>
              </view>
            </view>

            <!-- 客户列表 -->
            <view class="list-section">
              <view class="list-title">
                <text class="list-title-text">
                  客户列表
                </text>
                <text class="list-count">
                  ({{ customers.length }})
                </text>
              </view>
              <view class="customer-list">
                <view
                  v-for="(customer, index) in customers"
                  :key="index"
                  class="customer-item"
                >
                  <view class="customer-info">
                    <view class="customer-avatar">
                      <text class="avatar-text">
                        {{ customer.name.charAt(0) }}
                      </text>
                    </view>
                    <view class="customer-details">
                      <text class="customer-name">
                        {{ customer.name }}
                      </text>
                      <text class="customer-phone">
                        📱 {{ customer.phone }}
                      </text>
                      <text v-if="customer.wechat" class="customer-wechat">
                        💬 {{ customer.wechat }}
                      </text>
                      <text v-if="customer.address" class="customer-address">
                        📍 {{ customer.address }}
                      </text>
                    </view>
                  </view>
                  <view class="customer-actions">
                    <view class="action-btn edit" @click="editCustomer(index)">
                      <text class="action-icon">
                        ✏️
                      </text>
                    </view>
                    <view class="action-btn delete" @click="deleteCustomer(index)">
                      <text class="action-icon">
                        🗑️
                      </text>
                    </view>
                  </view>
                </view>

                <!-- 空状态 -->
                <view v-if="customers.length === 0" class="empty-state">
                  <text class="empty-icon">
                    👥
                  </text>
                  <text class="empty-text">
                    暂无客户信息
                  </text>
                  <text class="empty-subtitle">
                    添加您的第一个客户吧
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 品种管理弹窗 -->
    <view v-if="showBreedPopup" class="popup-overlay" @click="showBreedPopup = false">
      <view class="popup-container" @click.stop>
        <view class="management-popup">
          <view class="popup-header">
            <view class="popup-title">
              <text class="title-icon">
                🐰
              </text>
              <text class="title-text">
                品种管理
              </text>
            </view>
            <view class="popup-close" @click="showBreedPopup = false">
              <text class="close-icon">
                ✕
              </text>
            </view>
          </view>

          <view class="popup-content">
            <!-- 添加品种表单 -->
            <view class="add-form">
              <view class="form-title">
                <text class="form-title-text">
                  添加新品种
                </text>
              </view>
              <view class="form-grid">
                <view class="form-item">
                  <view class="form-label">
                    <text class="label-icon">
                      🐰
                    </text>
                    <text class="label-text">
                      品种名称
                    </text>
                  </view>
                  <u-input
                    v-model="breedForm.name"
                    placeholder="如：新西兰白兔"
                    class="modern-input"
                    border="none"
                    :custom-style="inputStyle"
                  />
                </view>

                <view class="form-item">
                  <view class="form-label">
                    <text class="label-icon">
                      📝
                    </text>
                    <text class="label-text">
                      品种描述
                    </text>
                  </view>
                  <u-input
                    v-model="breedForm.description"
                    placeholder="请输入品种特点描述"
                    class="modern-input"
                    border="none"
                    :custom-style="inputStyle"
                  />
                </view>
              </view>

              <view class="submit-section">
                <u-button
                  type="primary"
                  class="modern-submit-btn"
                  :custom-style="submitBtnStyle"
                  @click="addBreed"
                >
                  <text class="btn-icon">
                    ✨
                  </text>
                  <text class="btn-text">
                    添加品种
                  </text>
                </u-button>
              </view>
            </view>

            <!-- 品种列表 -->
            <view class="list-section">
              <view class="list-title">
                <text class="list-title-text">
                  品种列表
                </text>
                <text class="list-count">
                  ({{ breeds.length }})
                </text>
              </view>
              <view class="breed-list">
                <view
                  v-for="(breed, index) in breeds"
                  :key="index"
                  class="breed-item"
                >
                  <view class="breed-info">
                    <view class="breed-icon">
                      <text class="breed-emoji">
                        🐰
                      </text>
                    </view>
                    <view class="breed-details">
                      <text class="breed-name">
                        {{ breed.name }}
                      </text>
                      <text class="breed-description">
                        {{ breed.description }}
                      </text>
                    </view>
                  </view>
                  <view class="breed-actions">
                    <view class="action-btn edit" @click="editBreed(index)">
                      <text class="action-icon">
                        ✏️
                      </text>
                    </view>
                    <view class="action-btn delete" @click="deleteBreed(index)">
                      <text class="action-icon">
                        🗑️
                      </text>
                    </view>
                  </view>
                </view>

                <!-- 空状态 -->
                <view v-if="breeds.length === 0" class="empty-state">
                  <text class="empty-icon">
                    🐰
                  </text>
                  <text class="empty-text">
                    暂无品种信息
                  </text>
                  <text class="empty-subtitle">
                    添加您的第一个品种吧
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background: white;
  padding-bottom: 120rpx;
}

// 顶部导航栏
.top-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  padding: var(--status-bar-height, 44rpx) 0 0;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 40rpx;
  height: 88rpx;
}

.nav-left {
  display: flex;
  align-items: center;

  .menu-icon {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-right: 24rpx;
    cursor: pointer;

    .icon-line {
      width: 24rpx;
      height: 3rpx;
      background: #374151;
      margin: 2rpx 0;
      border-radius: 2rpx;
      transition: all 0.3s ease;
    }

    &:active .icon-line {
      background: #667eea;
    }
  }

  .nav-title-section {
    .nav-title {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #1a202c;
      line-height: 1;
      margin-bottom: 4rpx;
    }

    .nav-subtitle {
      font-size: 20rpx;
      color: #64748b;
    }
  }
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 20rpx;

  .notification-btn {
    position: relative;
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8fafc;
    border-radius: 12rpx;
    cursor: pointer;

    .notification-icon {
      font-size: 24rpx;
    }

    .notification-badge {
      position: absolute;
      top: -4rpx;
      right: -4rpx;
      width: 20rpx;
      height: 20rpx;
      background: #ef4444;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16rpx;
      color: white;
      font-weight: bold;
    }
  }

  .profile-avatar-small {
    width: 48rpx;
    height: 48rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .avatar-text {
      font-size: 20rpx;
      color: white;
      font-weight: bold;
    }
  }
}

.header-section {
  background: white;
  padding: 40rpx 40rpx 0;
  padding-top: calc(88rpx + var(--status-bar-height, 44rpx));
  position: relative;
  margin-top: 0;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  .bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.6;
  }

  .floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    .floating-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;

      &.circle-1 {
        width: 120rpx;
        height: 120rpx;
        top: 20%;
        left: 10%;
        animation-delay: 0s;
      }

      &.circle-2 {
        width: 80rpx;
        height: 80rpx;
        top: 60%;
        right: 15%;
        animation-delay: 2s;
      }

      &.circle-3 {
        width: 60rpx;
        height: 60rpx;
        bottom: 20%;
        left: 70%;
        animation-delay: 4s;
      }
    }
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20rpx) rotate(180deg);
  }
}

.header-content {
  position: relative;
  z-index: 1;
  margin-bottom: 40rpx;
}

// 用户信息卡片
.user-info-card {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  position: relative;

  .user-avatar-section {
    position: relative;
    margin-right: 32rpx;

    .user-avatar {
      width: 120rpx;
      height: 120rpx;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);

      .avatar-text-large {
        font-size: 48rpx;
        color: white;
        font-weight: bold;
      }
    }

    .edit-btn {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 40rpx;
      height: 40rpx;
      background: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      cursor: pointer;

      .edit-icon {
        font-size: 20rpx;
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .user-details {
    flex: 1;

    .user-name {
      display: block;
      font-size: 40rpx;
      font-weight: bold;
      color: #1a202c;
      margin-bottom: 8rpx;
    }

    .user-role {
      display: block;
      font-size: 26rpx;
      color: #64748b;
      margin-bottom: 16rpx;
    }

    .farm-info {
      display: flex;
      align-items: center;
      background: #f1f5f9;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      width: fit-content;

      .farm-icon {
        font-size: 20rpx;
        margin-right: 8rpx;
      }

      .farm-name {
        font-size: 24rpx;
        color: #64748b;
        font-weight: 500;
      }
    }
  }
}

.content-section {
  padding: 0 40rpx 40rpx;
  background: white;
}

// 功能菜单卡片
.menu-card {
  background: white;
  border-radius: 20rpx;
  padding: 28rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  margin-bottom: 24rpx;

  .card-header {
    margin-bottom: 24rpx;

    .card-title {
      display: flex;
      align-items: center;

      .title-icon {
        font-size: 32rpx;
        margin-right: 12rpx;
      }

      .title-text {
        font-size: 32rpx;
        font-weight: bold;
        color: #1a202c;
      }
    }
  }

  .menu-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16rpx;

    .menu-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 24rpx 16rpx;
      background: #f8fafc;
      border-radius: 16rpx;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;

      &:active {
        background: #f1f5f9;
        transform: translateY(-2rpx);
      }

      .menu-icon-wrapper {
        width: 64rpx;
        height: 64rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 12rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

        &.customer {
          background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        &.breed {
          background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
        }

        &.feed {
          background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
        }

        &.reports {
          background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }

        .menu-icon {
          font-size: 32rpx;
        }
      }

      .menu-text {
        font-size: 24rpx;
        font-weight: 500;
        color: #374151;
        text-align: center;
        margin-bottom: 8rpx;
      }

      .menu-arrow {
        position: absolute;
        top: 16rpx;
        right: 16rpx;
        width: 24rpx;
        height: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 50%;

        .arrow-icon {
          font-size: 16rpx;
          color: #64748b;
        }
      }
    }
  }

  .settings-list {
    .setting-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f1f5f9;
      cursor: pointer;
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background: #f8fafc;
        border-radius: 12rpx;
        margin: 0 -16rpx;
        padding: 20rpx 16rpx;
      }

      .setting-left {
        display: flex;
        align-items: center;
        flex: 1;

        .setting-icon-wrapper {
          width: 48rpx;
          height: 48rpx;
          border-radius: 12rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20rpx;

          &.settings {
            background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
            box-shadow: 0 4rpx 16rpx rgba(156, 39, 176, 0.3);
          }

          &.help {
            background: linear-gradient(135deg, #607d8b 0%, #455a64 100%);
            box-shadow: 0 4rpx 16rpx rgba(96, 125, 139, 0.3);
          }

          &.about {
            background: linear-gradient(135deg, #795548 0%, #5d4037 100%);
            box-shadow: 0 4rpx 16rpx rgba(121, 85, 72, 0.3);
          }

          .setting-icon {
            font-size: 24rpx;
          }
        }

        .setting-info {
          .setting-title {
            display: block;
            font-size: 28rpx;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 4rpx;
          }

          .setting-desc {
            font-size: 22rpx;
            color: #64748b;
          }
        }
      }

      .setting-arrow {
        .arrow-icon {
          font-size: 20rpx;
          color: #64748b;
        }
      }
    }
  }
}

// 退出登录卡片
.logout-card {
  background: white;
  border-radius: 20rpx;
  padding: 28rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(0, 0, 0, 0.06);

  .logout-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border-radius: 16rpx;
    cursor: pointer;
    transition: all 0.3s ease;

    &:active {
      background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
      transform: translateY(-1rpx);
    }

    .logout-icon {
      font-size: 24rpx;
      margin-right: 12rpx;
    }

    .logout-text {
      font-size: 28rpx;
      font-weight: bold;
      color: white;
    }
  }
}

// 弹窗遮罩层
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999; // 降低z-index，确保不会覆盖系统对话框
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.popup-container {
  width: 100%;
  max-width: 750rpx;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

// 管理弹窗样式
.management-popup {
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  max-height: calc(85vh - env(safe-area-inset-bottom)); // 考虑安全区域
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx 32rpx 24rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.06);
    flex-shrink: 0;

    .popup-title {
      display: flex;
      align-items: center;
      gap: 12rpx;

      .title-icon {
        font-size: 32rpx;
      }

      .title-text {
        font-size: 32rpx;
        font-weight: bold;
        color: #1a202c;
      }
    }

    .popup-close {
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f1f5f9;
      border-radius: 50%;
      cursor: pointer;

      .close-icon {
        font-size: 24rpx;
        color: #64748b;
      }

      &:active {
        background: #e2e8f0;
        transform: scale(0.95);
      }
    }
  }

  .popup-content {
    flex: 1;
    overflow-y: auto;
    padding: 32rpx;
    padding-bottom: calc(32rpx + env(safe-area-inset-bottom) + 120rpx); // 增加底部边距，避免被tabbar遮挡

    // 确保在小屏幕设备上也有足够的滚动空间
    min-height: 200rpx;

    // 添加滚动条样式（webkit浏览器）
    &::-webkit-scrollbar {
      width: 6rpx;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3rpx;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3rpx;

      &:hover {
        background: #a8a8a8;
      }
    }

    .add-form {
      margin-bottom: 40rpx;
      padding-bottom: 32rpx;
      border-bottom: 1rpx solid #f1f5f9;

      .form-title {
        margin-bottom: 24rpx;

        .form-title-text {
          font-size: 28rpx;
          font-weight: bold;
          color: #1a202c;
        }
      }

      .form-grid {
        display: flex;
        flex-direction: column;
        gap: 24rpx;

        .form-item {
          display: flex;
          flex-direction: column;
          gap: 12rpx;

          .form-label {
            display: flex;
            align-items: center;
            gap: 8rpx;

            .label-icon {
              font-size: 24rpx;
            }

            .label-text {
              font-size: 28rpx;
              font-weight: 500;
              color: #374151;
            }

            .required-mark {
              font-size: 24rpx;
              color: #ef4444;
              margin-left: 4rpx;
            }

            .optional-mark {
              font-size: 20rpx;
              color: #94a3b8;
              margin-left: 8rpx;
            }
          }
        }
      }

      .submit-section {
        margin-top: 32rpx;

        .modern-submit-btn {
          width: 100%;
          height: 88rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12rpx;
          border-radius: 16rpx;
          font-weight: bold;

          .btn-icon {
            font-size: 24rpx;
          }

          .btn-text {
            font-size: 28rpx;
          }
        }
      }
    }

    .list-section {
      .list-title {
        display: flex;
        align-items: center;
        margin-bottom: 24rpx;

        .list-title-text {
          font-size: 28rpx;
          font-weight: bold;
          color: #1a202c;
        }

        .list-count {
          font-size: 24rpx;
          color: #64748b;
          margin-left: 8rpx;
        }
      }
    }
  }
}

// 客户列表样式
.customer-list {
  .customer-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx;
    background: #f8fafc;
    border-radius: 16rpx;
    margin-bottom: 16rpx;
    transition: all 0.3s ease;

    &:active {
      background: #f1f5f9;
      transform: translateY(-1rpx);
    }

    .customer-info {
      display: flex;
      align-items: center;
      flex: 1;

      .customer-avatar {
        width: 64rpx;
        height: 64rpx;
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;

        .avatar-text {
          font-size: 24rpx;
          color: white;
          font-weight: bold;
        }
      }

      .customer-details {
        flex: 1;

        .customer-name {
          display: block;
          font-size: 28rpx;
          font-weight: 600;
          color: #1a202c;
          margin-bottom: 4rpx;
        }

        .customer-phone {
          display: block;
          font-size: 22rpx;
          color: #64748b;
          margin-bottom: 4rpx;
        }

        .customer-wechat {
          display: block;
          font-size: 22rpx;
          color: #10b981;
          margin-bottom: 4rpx;
        }

        .customer-address {
          font-size: 20rpx;
          color: #94a3b8;
        }
      }
    }

    .customer-actions {
      display: flex;
      gap: 12rpx;

      .action-btn {
        width: 48rpx;
        height: 48rpx;
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;

        &.edit {
          background: rgba(59, 130, 246, 0.1);

          .action-icon {
            font-size: 20rpx;
          }

          &:active {
            background: rgba(59, 130, 246, 0.2);
            transform: scale(0.95);
          }
        }

        &.delete {
          background: rgba(239, 68, 68, 0.1);

          .action-icon {
            font-size: 20rpx;
          }

          &:active {
            background: rgba(239, 68, 68, 0.2);
            transform: scale(0.95);
          }
        }
      }
    }
  }
}

// 品种列表样式
.breed-list {
  .breed-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx;
    background: #f8fafc;
    border-radius: 16rpx;
    margin-bottom: 16rpx;
    transition: all 0.3s ease;

    &:active {
      background: #f1f5f9;
      transform: translateY(-1rpx);
    }

    .breed-info {
      display: flex;
      align-items: center;
      flex: 1;

      .breed-icon {
        width: 64rpx;
        height: 64rpx;
        background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;

        .breed-emoji {
          font-size: 32rpx;
        }
      }

      .breed-details {
        flex: 1;

        .breed-name {
          display: block;
          font-size: 28rpx;
          font-weight: 600;
          color: #1a202c;
          margin-bottom: 4rpx;
        }

        .breed-description {
          font-size: 22rpx;
          color: #64748b;
          line-height: 1.4;
        }
      }
    }

    .breed-actions {
      display: flex;
      gap: 12rpx;

      .action-btn {
        width: 48rpx;
        height: 48rpx;
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;

        &.edit {
          background: rgba(59, 130, 246, 0.1);

          .action-icon {
            font-size: 20rpx;
          }

          &:active {
            background: rgba(59, 130, 246, 0.2);
            transform: scale(0.95);
          }
        }

        &.delete {
          background: rgba(239, 68, 68, 0.1);

          .action-icon {
            font-size: 20rpx;
          }

          &:active {
            background: rgba(239, 68, 68, 0.2);
            transform: scale(0.95);
          }
        }
      }
    }
  }
}

// 空状态样式
.empty-state {
  text-align: center;
  padding: 60rpx 40rpx;

  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 24rpx;
    display: block;
    opacity: 0.6;
  }

  .empty-text {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #64748b;
    margin-bottom: 12rpx;
  }

  .empty-subtitle {
    font-size: 24rpx;
    color: #94a3b8;
  }
}
</style>
