# 基于布局系统的自定义TabBar实现说明

## 核心思路
基于UniLayouts布局系统的纯自定义tabbar最佳实现方案：
1. **pages.json中完全不配置tabBar** - 避免原生tabbar的所有问题
2. **使用布局系统统一管理tabbar** - 在default.vue布局中实现
3. **支持用户角色权限控制** - 根据角色动态显示不同tab
4. **完全自定义实现** - 无需处理原生tabbar的隐藏和显示

## 实现步骤

### 1. pages.json中不配置tabBar
```json
{
  "pages": [
    {
      "path": "pages/statistics/statistics",
      "type": "home",
      "layout": "default"
    },
    {
      "path": "pages/profile/profile",
      "layout": "default"
    }
  ]
}
```

### 2. 创建布局系统的tabbar组件
```vue
<!-- src/layouts/default.vue -->
<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'

// 当前激活的标签
const activeTab = ref('statistics')

// 根据当前页面路径初始化激活标签
onMounted(() => {
  const currentPage = getCurrentPages().pop()
  if (currentPage) {
    const route = `/${currentPage.route}`
    for (const [tab, path] of Object.entries(pages)) {
      if (route === path) {
        activeTab.value = tab
        break
      }
    }
  }
})

// 导航到指定页面
function navigateTo(url: string, tab: string) {
  if (activeTab.value !== tab) {
    activeTab.value = tab
    uni.navigateTo({
      url,
      fail: (err) => {
        console.error('页面跳转失败:', err)
      }
    })
  }
}

// 模拟用户角色（可以从store获取）
const userRole = ref('admin') // admin, manager, user

// 根据用户角色过滤可显示的tabs
const availableTabs = computed(() => {
  const allTabs = [
    {
      key: 'statistics',
      label: '统计',
      icon: '📊',
      path: '/pages/statistics/statistics',
      roles: ['admin', 'manager', 'user']
    },
    {
      key: 'profile',
      label: '我的',
      icon: '👤',
      path: '/pages/profile/profile',
      roles: ['admin', 'manager', 'user']
    },
    {
      key: 'admin',
      label: '管理',
      icon: '⚙️',
      path: '/pages/admin/admin',
      roles: ['admin']
    }
  ]

  return allTabs.filter(tab => tab.roles.includes(userRole.value))
})
</script>

<template>
  <view class="layout-container">
    <!-- 页面内容插槽 -->
    <slot />

    <!-- 自定义底部 Tabbar -->
    <view class="custom-tabbar">
      <view
        v-for="tab in availableTabs"
        :key="tab.key"
        class="tabbar-item"
        :class="{ 'tabbar-active': activeTab === tab.key }"
        @tap="navigateTo(tab.path, tab.key)"
      >
        <view class="tabbar-icon">
          <text class="emoji">{{ tab.icon }}</text>
        </view>
        <text class="tabbar-text">{{ tab.label }}</text>
      </view>
    </view>
  </view>
</template>
```

### 3. 页面自动使用布局
```vue
<!-- pages/statistics/statistics.vue -->
<route lang="json">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "销售统计"
  }
}
</route>

<script setup>
// 页面代码，无需手动处理tabbar
import { useSalesStore } from '@/store/sales'

const salesStore = useSalesStore()

onMounted(() => {
  salesStore.loadSalesData()
})
</script>

<template>
  <view class="page">
    <!-- 页面内容 -->
    <!-- tabbar会自动通过布局系统添加 -->
  </view>
</template>
```

## 关键要点

1. **完全无原生tabbar**: pages.json中完全不配置tabBar，避免所有原生tabbar问题
2. **布局系统管理**: 使用UniLayouts在default.vue中统一管理tabbar
3. **纯自定义实现**: 无需处理原生tabbar的隐藏显示，完全自主控制
4. **角色权限控制**: 根据用户角色动态显示不同的tab
5. **页面自动应用**: 所有使用default布局的页面自动获得tabbar
6. **无需手动引入**: 页面代码更简洁，无需手动处理tabbar

## 优势

- ✅ 完全自定义样式和交互
- ✅ 支持不同用户角色显示不同tabbar
- ✅ 可以添加徽章、红点等功能
- ✅ 不需要隐藏原生tabbar，避免闪烁问题
- ✅ 组件化设计，易于维护和复用
- ✅ 跨平台兼容性好

## 测试验证

- [ ] 不同用户角色显示不同的tab选项
- [ ] 点击tabbar能正确切换页面
- [ ] 页面刷新后tabbar状态正确
- [ ] 没有原生tabbar闪烁问题
- [ ] 组件可以在多个页面复用
