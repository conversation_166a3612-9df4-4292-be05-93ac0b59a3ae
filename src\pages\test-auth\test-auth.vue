<route lang="json">
{
  "type": "page",
  "layout": "default",
  "style": {
    "navigationBarTitleText": "认证测试页面"
  }
}
</route>

<template>
  <view class="test-auth-page">
    <view class="header">
      <text class="title">认证测试页面</text>
      <text class="subtitle">这个页面需要登录后才能访问</text>
    </view>

    <view class="user-info">
      <text class="label">当前用户信息：</text>
      <view class="info-item">
        <text>用户名: {{ userStore.userInfo.username || '未获取' }}</text>
      </view>
      <view class="info-item">
        <text>手机号: {{ userStore.userInfo.phone || '未获取' }}</text>
      </view>
      <view class="info-item">
        <text>Token: {{ userStore.getToken() ? '已获取' : '未获取' }}</text>
      </view>
      <view class="info-item">
        <text>登录状态: {{ userStore.isLoggedIn() ? '已登录' : '未登录' }}</text>
      </view>
    </view>

    <view class="actions">
      <button @click="testRequest" class="test-btn">测试API请求</button>
      <button @click="logout" class="logout-btn">退出登录</button>
      <button @click="goToProfile" class="nav-btn">跳转到个人中心</button>
    </view>

    <view class="logs">
      <text class="label">操作日志：</text>
      <view v-for="(log, index) in logs" :key="index" class="log-item">
        <text>{{ log }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useUserStore } from '@/store'

const userStore = useUserStore()
const logs = ref<string[]>([])

function addLog(message: string) {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${message}`)
  if (logs.value.length > 10) {
    logs.value.pop()
  }
}

// 测试API请求
async function testRequest() {
  try {
    addLog('开始测试API请求...')
    // 这里可以调用一个需要认证的API
    const userInfo = await userStore.getUserInfo()
    addLog('API请求成功')
    console.log('用户信息:', userInfo)
  } catch (error) {
    addLog(`API请求失败: ${error}`)
    console.error('API请求失败:', error)
  }
}

// 退出登录
async function logout() {
  try {
    addLog('开始退出登录...')
    await userStore.logout()
    addLog('退出登录成功，即将跳转到登录页')
    
    // 延迟一下让用户看到日志
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/login'
      })
    }, 1000)
  } catch (error) {
    addLog(`退出登录失败: ${error}`)
    console.error('退出登录失败:', error)
  }
}

// 跳转到个人中心
function goToProfile() {
  addLog('跳转到个人中心...')
  uni.navigateTo({
    url: '/pages/profile/profile'
  })
}

// 页面加载时添加日志
addLog('页面加载完成，认证检查通过')
</script>

<style lang="scss" scoped>
.test-auth-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  
  .title {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
  }
  
  .subtitle {
    display: block;
    font-size: 14px;
    color: #666;
  }
}

.user-info {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  
  .label {
    display: block;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
  }
  
  .info-item {
    margin-bottom: 10px;
    
    text {
      font-size: 14px;
      color: #666;
    }
  }
}

.actions {
  margin-bottom: 20px;
  
  .test-btn, .logout-btn, .nav-btn {
    width: 100%;
    margin-bottom: 10px;
    padding: 12px;
    border-radius: 6px;
    font-size: 16px;
  }
  
  .test-btn {
    background-color: #007aff;
    color: white;
  }
  
  .logout-btn {
    background-color: #ff3b30;
    color: white;
  }
  
  .nav-btn {
    background-color: #34c759;
    color: white;
  }
}

.logs {
  background: white;
  border-radius: 8px;
  padding: 20px;
  
  .label {
    display: block;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
  }
  
  .log-item {
    margin-bottom: 8px;
    padding: 8px;
    background-color: #f8f8f8;
    border-radius: 4px;
    
    text {
      font-size: 12px;
      color: #666;
      font-family: monospace;
    }
  }
}
</style>
