import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface SaleRecord {
  id: string
  breed: string
  quantity: number
  price: number
  total: number
  date: string
  createdAt: string
}

export const useSalesStore = defineStore('sales', () => {
  // 状态
  const salesRecords = ref<SaleRecord[]>([])
  
  // 计算属性
  const totalSales = computed(() => {
    return salesRecords.value.reduce((sum, record) => sum + record.total, 0)
  })
  
  const totalQuantity = computed(() => {
    return salesRecords.value.reduce((sum, record) => sum + record.quantity, 0)
  })
  
  const todaySales = computed(() => {
    const today = new Date().toISOString().split('T')[0]
    return salesRecords.value
      .filter(record => record.date === today)
      .reduce((sum, record) => sum + record.total, 0)
  })
  
  const recentSales = computed(() => {
    return salesRecords.value
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 10)
  })
  
  // 方法
  const addSale = (saleData: Omit<SaleRecord, 'id' | 'createdAt'>) => {
    const newSale: SaleRecord = {
      ...saleData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString()
    }
    
    salesRecords.value.push(newSale)
    saveSalesData()
  }
  
  const deleteSale = (id: string) => {
    const index = salesRecords.value.findIndex(record => record.id === id)
    if (index > -1) {
      salesRecords.value.splice(index, 1)
      saveSalesData()
    }
  }
  
  const updateSale = (id: string, updateData: Partial<SaleRecord>) => {
    const index = salesRecords.value.findIndex(record => record.id === id)
    if (index > -1) {
      salesRecords.value[index] = { ...salesRecords.value[index], ...updateData }
      saveSalesData()
    }
  }
  
  const getSalesByDateRange = (startDate: string, endDate: string) => {
    return salesRecords.value.filter(record => {
      return record.date >= startDate && record.date <= endDate
    })
  }
  
  const getSalesByBreed = (breed: string) => {
    return salesRecords.value.filter(record => record.breed === breed)
  }
  
  const getMonthlyStats = (year: number, month: number) => {
    const monthStr = `${year}-${month.toString().padStart(2, '0')}`
    const monthlyRecords = salesRecords.value.filter(record => 
      record.date.startsWith(monthStr)
    )
    
    return {
      totalSales: monthlyRecords.reduce((sum, record) => sum + record.total, 0),
      totalQuantity: monthlyRecords.reduce((sum, record) => sum + record.quantity, 0),
      recordCount: monthlyRecords.length,
      averagePrice: monthlyRecords.length > 0 
        ? monthlyRecords.reduce((sum, record) => sum + record.price, 0) / monthlyRecords.length 
        : 0
    }
  }
  
  const saveSalesData = () => {
    try {
      uni.setStorageSync('salesRecords', salesRecords.value)
    } catch (error) {
      console.error('保存销售数据失败:', error)
    }
  }
  
  const loadSalesData = () => {
    try {
      const data = uni.getStorageSync('salesRecords')
      if (data && Array.isArray(data)) {
        salesRecords.value = data
      }
    } catch (error) {
      console.error('加载销售数据失败:', error)
    }
  }
  
  const clearAllData = () => {
    salesRecords.value = []
    saveSalesData()
  }
  
  // 初始化示例数据
  const initSampleData = () => {
    if (salesRecords.value.length === 0) {
      const sampleData: SaleRecord[] = [
        {
          id: '1',
          breed: '新西兰白兔',
          quantity: 5,
          price: 80,
          total: 400,
          date: '2024-01-15',
          createdAt: '2024-01-15T10:30:00.000Z'
        },
        {
          id: '2',
          breed: '比利时兔',
          quantity: 3,
          price: 120,
          total: 360,
          date: '2024-01-14',
          createdAt: '2024-01-14T14:20:00.000Z'
        },
        {
          id: '3',
          breed: '安哥拉兔',
          quantity: 2,
          price: 200,
          total: 400,
          date: '2024-01-13',
          createdAt: '2024-01-13T09:15:00.000Z'
        }
      ]
      
      salesRecords.value = sampleData
      saveSalesData()
    }
  }
  
  return {
    // 状态
    salesRecords,
    
    // 计算属性
    totalSales,
    totalQuantity,
    todaySales,
    recentSales,
    
    // 方法
    addSale,
    deleteSale,
    updateSale,
    getSalesByDateRange,
    getSalesByBreed,
    getMonthlyStats,
    loadSalesData,
    saveSalesData,
    clearAllData,
    initSampleData
  }
})
