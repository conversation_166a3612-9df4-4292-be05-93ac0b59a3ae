import type { IUserInfoVo } from '@/api/types/login'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import {
  getUserInfo as _getUserInfo,
  login as _login,
  logout as _logout,
  wxLogin as _wxLogin,
  getWxCode,
} from '@/api/login'
import { toast } from '@/utils/toast'

// 扩展用户信息接口，添加token字段
interface IUserInfoWithToken extends IUserInfoVo {
  token?: string
}

// 初始化状态
const userInfoState: IUserInfoWithToken = {
  id: 0,
  phone: '',
  username: '',
  avatar: '/static/images/default-avatar.png',
  is_active: false,
  is_superuser: false,
  created_at: '',
  updated_at: '',
  token: '',
}

export const useUserStore = defineStore(
  'user',
  () => {
    // 定义用户信息
    const userInfo = ref<IUserInfoWithToken>({ ...userInfoState })

    // 获取token
    const getToken = () => {
      return userInfo.value.token || uni.getStorageSync('token') || ''
    }

    // 设置token
    const setToken = (token: string) => {
      userInfo.value.token = token
      uni.setStorageSync('token', token)
    }

    // 检查是否已登录
    const isLoggedIn = () => {
      const token = getToken()
      const hasUserInfo = !!userInfo.value.username
      return !!(token && hasUserInfo)
    }

    // 设置用户信息
    const setUserInfo = (val: IUserInfoVo) => {
      console.log('设置用户信息', val)
      if (!val) {
        console.error('用户信息为空')
        return
      }
      // 若头像为空 则使用默认头像
      if (!val.avatar) {
        val.avatar = userInfoState.avatar
      }
      else {
        val.avatar = 'https://oss.laf.run/ukw0y1-site/avatar.jpg?feige'
      }
      userInfo.value = { ...userInfo.value, ...val }
    }
    const setUserAvatar = (avatar: string) => {
      userInfo.value.avatar = avatar
      console.log('设置用户头像', avatar)
      console.log('userInfo', userInfo.value)
    }
    // 删除用户信息
    const removeUserInfo = () => {
      userInfo.value = { ...userInfoState }
      uni.removeStorageSync('userInfo')
      uni.removeStorageSync('token')
    }

    // 清除登录状态
    const clearAuth = () => {
      removeUserInfo()
      // 可以在这里添加其他清理逻辑
    }
    /**
     * 获取用户信息
     */
    const getUserInfo = async () => {
      const res = await _getUserInfo()
      console.log('获取用户信息响应:', res)
      // 根据实际响应结构获取用户信息
      const userInfoData = res.data || res
      if (userInfoData) {
        setUserInfo(userInfoData)
        // 不需要手动存储，Pinia持久化会自动处理
      } else {
        console.error('未能获取到用户信息:', res)
        throw new Error('获取用户信息失败')
      }
      // TODO 这里可以增加获取用户路由的方法 根据用户的角色动态生成路由
      return res
    }
    /**
     * 用户登录
     * @param credentials 登录参数
     * @returns R<IUserLogin>
     */
    const login = async (credentials: {
      phone: string
      password: string
    }) => {
      const res = await _login(credentials)
      console.log('登录信息', res)

      // 保存token - 根据实际响应结构获取token
      const token = res.data?.access_token || res.access_token
      if (token) {
        // 设置token
        setToken(token)
      } else {
        console.error('未能获取到access_token:', res)
        throw new Error('登录响应中未找到access_token')
      }

      toast.success('登录成功')
      await getUserInfo()
      return res
    }

    /**
     * 退出登录 并 删除用户信息
     */
    const logout = async () => {
      try {
        // 只有在有token的情况下才调用退出登录API
        const token = getToken()
        if (token) {
          await _logout()
        }
      } catch (error) {
        console.error('退出登录API调用失败:', error)
        // API调用失败不影响本地清理
      } finally {
        // 无论API调用是否成功，都清除本地状态
        clearAuth()
      }
    }
    /**
     * 微信登录
     */
    const wxLogin = async () => {
      // 获取微信小程序登录的code
      const data = await getWxCode()
      console.log('微信登录code', data)

      const res = await _wxLogin(data)
      await getUserInfo()
      return res
    }

    return {
      userInfo,
      getToken,
      setToken,
      isLoggedIn,
      setUserInfo,
      clearAuth,
      login,
      wxLogin,
      getUserInfo,
      setUserAvatar,
      logout,
    }
  },
  {
    persist: true,
  },
)
